<!DOCTYPE html>
<html lang="en" dir="ltr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Network Solutions - MCT</title>
    <link rel="stylesheet" href="css/style.css">
    <link rel="stylesheet" href="css/responsive.css">
    <link rel="stylesheet" href="css/device-adaptive.css">
    <link rel="stylesheet" href="css/service-pages-navbar.css">
    <link rel="stylesheet" href="css/mobile-fixes.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body class="service-page">
    <!-- شاشة التحميل -->
    <div class="page-loader" id="pageLoader">
        <div class="loader-logo">
            <img src="image/logo-8.png" alt="MCT Logo">
            <div class="loader-text">Network Solutions</div>
        </div>
    </div>

    <div class="animated-bg"></div>
    <div class="floating-particles" id="particles"></div>
    <nav class="navbar">
        <div class="nav-container">
            <div class="logo-section">
                <a href="index-en.html" class="logo">
                    <img src="image/logo-11.png" alt="MCT Logo">
                </a>
                <a href="index-en.html" class="logo">
                    <img src="image/logo-10.png" alt="MCT Logo">
                </a>
            </div>
            <div class="nav-center">
                <button class="hamburger" id="hamburger-menu" aria-label="Open menu">
                    <span></span>
                    <span></span>
                    <span></span>
                </button>
                <ul class="nav-links" id="nav-links">
                    <li><a href="networking-en.html" class="active">Network Solutions</a></li>
                    <li><a href="software-en.html">Software Development</a></li>
                    <li><a href="cybersecurity-en.html">Information Security</a></li>
                    <li><a href="maintenance-en.html">Support & Maintenance</a></li>
                    <li><a href="index-en.html">Back to Home</a></li>
                    <li><button id="lang-toggle" onclick="switchLanguage('ar')">AR</button></li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <main class="main-content">
        <!-- Service Hero -->
        <section class="service-hero fade-in-element">
            <div class="service-icon"><i class="fas fa-network-wired"></i></div>
            <h1 class="service-title fade-in-element delay-1">Network Solutions</h1>
            <p class="service-subtitle fade-in-element delay-2">
                We provide integrated and reliable network solutions that ensure smooth and secure connectivity for all your systems. 
                From design and implementation to maintenance and support, we are your trusted partner in the networking world.
            </p>
        </section>

        <!-- Our Services -->
        <section class="content-section fade-in-element delay-3">
            <h2 class="section-title fade-in-element">Our Network Services</h2>
            <div class="services-with-animation">
                <div class="services-text">
                    <ul class="services-list fade-in-element delay-1">
                        <li><strong>Network Design:</strong> Custom network design that suits your needs and business size</li>
                        <li><strong>Implementation and Installation:</strong> Professional network implementation using the latest equipment and technologies</li>
                        <li><strong>Wireless Networks:</strong> Advanced and secure Wi-Fi solutions for all environments</li>
                        <li><strong>Server Networks:</strong> Setup and management of high-performance server networks</li>
                        <li><strong>Regular Maintenance:</strong> Comprehensive maintenance services to ensure network continuity</li>
                        <li><strong>Network Monitoring:</strong> Continuous performance monitoring and early problem detection</li>
                        <li><strong>Network Upgrades:</strong> Updating and upgrading existing networks to improve performance</li>
                    </ul>
                </div>
                <div class="services-animation">
                    <div class="network-globe-container">
                        <div class="network-container" id="networkContainer">
                            
                            <div class="floating-particles" id="particles"></div>
                            
                            <svg class="connection-svg">
                                <path class="curved-path" d="M 300 160 Q 380 190 440 300" />
                                <path class="curved-path" d="M 440 300 Q 380 410 300 440" />
                                <path class="curved-path" d="M 300 440 Q 220 410 160 300" />
                                <path class="curved-path" d="M 160 300 Q 220 190 300 160" />
                            </svg>
                            
                            <div class="globe"></div>
                            
                            <div class="user-avatar user1">
                                <div class="avatar-head"></div>
                                <div class="avatar-body"></div>
                            </div>
                            
                            <div class="user-avatar user2">
                                <div class="avatar-head"></div>
                                <div class="avatar-body"></div>
                            </div>
                            
                            <div class="user-avatar user3">
                                <div class="avatar-head"></div>
                                <div class="avatar-body"></div>
                            </div>
                            
                            <div class="user-avatar user4">
                                <div class="avatar-head"></div>
                                <div class="avatar-body"></div>
                            </div>
                            
                            <div class="moving-dot dot1"></div>
                            <div class="moving-dot dot2"></div>
                            <div class="moving-dot dot3"></div>
                            <div class="moving-dot dot4"></div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <section class="content-section fade-in-element delay-4">
            <h2 class="section-title fade-in-element">Why Choose Our Services?</h2>
            <div class="features-grid">
                <div class="feature-card fade-in-element delay-1">
                    <h3>High Performance</h3>
                    <p>High-speed and efficient networks that ensure optimal performance for all your applications and systems</p>
                </div>
                <div class="feature-card fade-in-element delay-2">
                    <h3>Complete Reliability</h3>
                    <p>Reliable network solutions with quality guarantees and backup plans for protection against failures</p>
                </div>
                <div class="feature-card fade-in-element delay-3">
                    <h3>Advanced Security</h3>
                    <p>Implementation of the highest security standards to protect your network from threats and unauthorized access</p>
                </div>
                <div class="feature-card fade-in-element delay-4">
                    <h3>Scalability</h3>
                    <p>Flexible networks that can be expanded and developed with your business growth and future requirements</p>
                </div>
                <div class="feature-card fade-in-element delay-5">
                    <h3>Excellent Technical Support</h3>
                    <p>Specialized technical support team available 24/7 to solve any problems or inquiries</p>
                </div>
                <div class="feature-card fade-in-element delay-6">
                    <h3>Optimal Cost</h3>
                    <p>Cost-effective solutions that achieve the best return on investment for your projects</p>
                </div>
            </div>
        </section>

        <!-- Call to Action -->
        <section class="contact-cta fade-in-element delay-5">
            <h2 style="color: #008B80; margin-bottom: 20px;">Do You Need a Reliable and High-Performance Network?</h2>
            <p style="margin-bottom: 30px; color: #c0c0c0;">Contact us today for a free consultation and comprehensive assessment of your networking needs</p>
            <a href="index-en.html#contact" class="cta-button">Contact Us</a>
            <a href="tel:+966123456789" class="cta-button">Call Us</a>
        </section>
    </main>

    <script>
        // إنشاء الجسيمات المتحركة
        function createParticles() {
            const particlesContainer = document.getElementById('particles');
            const particleCount = 30;

            for (let i = 0; i < particleCount; i++) {
                const particle = document.createElement('div');
                particle.className = 'particle';
                particle.style.left = Math.random() * 100 + '%';
                particle.style.top = Math.random() * 100 + '%';
                particle.style.animationDelay = Math.random() * 8 + 's';
                particle.style.animationDuration = (Math.random() * 4 + 4) + 's';
                particlesContainer.appendChild(particle);
            }
        }

        // تأثيرات الفيديو التفاعلية المحسنة
        function setupVideoInteraction() {
            const video = document.getElementById('bgVideo');

            if (!video) {
                console.log('Background video element not found');
                return;
            }

            console.log('Setting up background video interaction...');

            let isMouseInside = true;
            let mouseTimeout;

            // إعدادات الفيديو
            video.muted = true;
            video.loop = true;
            video.autoplay = true;
            video.playsInline = true;

            // محاولة تشغيل الفيديو فور<|im_start|>
            const playVideo = () => {
                video.play().then(() => {
                    console.log('Background video started playing');
                }).catch(e => {
                    console.log('Background video play failed:', e);
                    // المحاولة مرة أخرى بعد ثانية
                    setTimeout(() => {
                        video.play().catch(err => console.log('Background video second attempt failed:', err));
                    }, 1000);
                });
            };

            // تشغيل الفيديو عند التحميل
            video.addEventListener('loadeddata', playVideo);
            video.addEventListener('canplay', playVideo);

            // التشغيل فور<|im_start|> إذا كان الفيديو جاهز
            if (video.readyState >= 3) {
                playVideo();
            }

            // تشغيل الفيديو عند دخول الماوس للصفحة
            document.addEventListener('mouseenter', () => {
                isMouseInside = true;
                if (video.paused) {
                    playVideo();
                }
            });

            // إيقاف الفيديو عند خروج الماوس من الصفحة
            document.addEventListener('mouseleave', () => {
                isMouseInside = false;
                video.pause();
                video.style.transform = 'scale(1) translate(0px, 0px)';
            });

            // تحريك الفيديو مع الماوس
            document.addEventListener('mousemove', (e) => {
                if (!isMouseInside) return;

                const mouseX = e.clientX / window.innerWidth;
                const mouseY = e.clientY / window.innerHeight;

                // حركة أكثر سلاسة ووضوح
                const moveX = (mouseX - 0.5) * 20;
                const moveY = (mouseY - 0.5) * 20;

                video.style.transform = `scale(1.05) translate(${moveX}px, ${moveY}px)`;

                // استئناف الفيديو إذا كان متوقف
                clearTimeout(mouseTimeout);
                if (video.paused && isMouseInside) {
                    playVideo();
                }

                // إيقاف الفيديو بعد 3 ثوان من عدم الحركة
                mouseTimeout = setTimeout(() => {
                    if (isMouseInside) {
                        video.pause();
                    }
                }, 3000);
            });

            // تشغيل الفيديو عند النقر على الصفحة
            document.addEventListener('click', playVideo, { once: true });

            // تشغيل الفيديو عند اللمس (للأجهزة المحمولة)
            document.addEventListener('touchstart', playVideo, { once: true });
        }

        // إعداد فيديو الخدمات
        function setupServicesVideo() {
            const servicesVideo = document.getElementById('servicesVideo');
            const videoContainer = document.querySelector('.services-video');

            if (!servicesVideo || !videoContainer) {
                console.log('Services video element not found');
                return;
            }

            console.log('Setting up services video...');

            // إعدادات الفيديو
            servicesVideo.muted = true;
            servicesVideo.loop = true;
            servicesVideo.playsInline = true;

            // تشغيل الفيديو عند دخول الماوس
            videoContainer.addEventListener('mouseenter', () => {
                servicesVideo.play().then(() => {
                    console.log('Services video resumed playing from:', servicesVideo.currentTime.toFixed(2) + 's');
                }).catch(e => {
                    console.log('Services video play failed:', e);
                });
            });

            // إيقاف الفيديو عند خروج الماوس (الاحتفاظ بالموضع)
            videoContainer.addEventListener('mouseleave', () => {
                servicesVideo.pause();
                console.log('Services video paused at:', servicesVideo.currentTime.toFixed(2) + 's');
                // لا نعيد تعيين currentTime حتى يكمل من نفس النقطة
            });

            // تشغيل/إيقاف الفيديو عند النقر
            videoContainer.addEventListener('click', () => {
                if (servicesVideo.paused) {
                    servicesVideo.play().then(() => {
                        console.log('Services video manually started from:', servicesVideo.currentTime.toFixed(2) + 's');
                    }).catch(e => {
                        console.log('Services video click play failed:', e);
                    });
                } else {
                    servicesVideo.pause();
                    console.log('Services video manually paused at:', servicesVideo.currentTime.toFixed(2) + 's');
                }
            });

            // التأكد من أن الفيديو يبدأ من البداية في أول مرة فقط
            servicesVideo.addEventListener('loadeddata', () => {
                if (servicesVideo.currentTime === 0) {
                    console.log('Services video loaded and ready');
                }
            });
        }

        // Language Switch Function
        function switchLanguage(lang) {
            if (lang === 'ar') {
                sessionStorage.setItem('pageTransition', 'true');
                window.location.href = 'networking.html';
            }
        }

        // إدارة تحميل الصفحة
        function handlePageLoad() {
            const pageLoader = document.getElementById('pageLoader');
            const isTransition = sessionStorage.getItem('pageTransition');

            if (isTransition) {
                sessionStorage.removeItem('pageTransition');
                setTimeout(() => pageLoader.classList.add('fade-out'), 2000);
                setTimeout(() => pageLoader.style.display = 'none', 2800);
            } else {
                pageLoader.style.display = 'none';
            }
        }

        // تهيئة الصفحة
        window.addEventListener('load', () => {
            handlePageLoad();
            createParticles();
            setupNetworkGlobe();
        });

        // إعداد الشكل التفاعلي للشبكة العالمية
        function setupNetworkGlobe() {
            const networkContainer = document.getElementById('networkContainer');
            const particlesContainer = document.getElementById('particles');
            const globe = document.querySelector('.globe');
            
            if (!networkContainer || !particlesContainer || !globe) {
                console.log('Network globe elements not found');
                return;
            }

            console.log('Setting up network globe...');

            // إنشاء الجسيمات المتحركة
            function createGlobeParticles() {
                for (let i = 0; i < 15; i++) {
                    const particle = document.createElement('div');
                    particle.className = 'particle';
                    particle.style.left = Math.random() * 100 + '%';
                    particle.style.top = Math.random() * 100 + '%';
                    particle.style.animationDelay = Math.random() * 6 + 's';
                    particle.style.animationDuration = (Math.random() * 4 + 4) + 's';
                    particlesContainer.appendChild(particle);
                }
            }

            // دوران الكرة الأرضية
            let rotationAngle = 0;
            function rotateGlobe() {
                rotationAngle += 0.5;
                globe.style.transform = `translate(-50%, -50%) rotate(${rotationAngle}deg)`;
                requestAnimationFrame(rotateGlobe);
            }

            // تأثير النقر
            networkContainer.addEventListener('click', (e) => {
                const ripple = document.createElement('div');
                ripple.style.position = 'absolute';
                ripple.style.width = '20px';
                ripple.style.height = '20px';
                ripple.style.background = 'rgba(0, 198, 255, 0.5)';
                ripple.style.borderRadius = '50%';
                ripple.style.left = (e.clientX - networkContainer.getBoundingClientRect().left - 10) + 'px';
                ripple.style.top = (e.clientY - networkContainer.getBoundingClientRect().top - 10) + 'px';
                ripple.style.animation = 'ripple 0.6s ease-out forwards';
                ripple.style.pointerEvents = 'none';
                ripple.style.zIndex = '10';
                
                networkContainer.appendChild(ripple);
                
                setTimeout(() => {
                    ripple.remove();
                }, 600);
            });

            // بدء الحركة
            createGlobeParticles();
            rotateGlobe();
        }
    </script>
    <script src="js/dashboard-animation.js"></script>
    <script src="js/scroll-animations.js"></script>
    <script src="js/mobile-nav.js"></script>
    <script src="js/device-detection.js"></script>
</body>
</html>
