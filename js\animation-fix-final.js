/* ===================================
   FINAL ANIMATION FIX - PREVENT TAB SWITCHING SPEED ISSUES
   ===================================*/

(function() {
    'use strict';
    
    let isPageVisible = true;
    let animationElements = [];
    let originalStates = new Map();
    
    // Initialize when DOM is ready
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', init);
    } else {
        init();
    }
    
    function init() {
        console.log('Animation Fix: Initializing...');
        
        // Collect all animated elements
        collectAnimatedElements();
        
        // Store original animation states
        storeOriginalStates();
        
        // Set up event listeners
        setupEventListeners();
        
        // Force correct animation properties
        forceCorrectAnimations();
        
        console.log('Animation Fix: Initialized with', animationElements.length, 'elements');
    }
    
    function collectAnimatedElements() {
        // Clear previous collection
        animationElements = [];
        
        // Hero logo
        const heroLogo = document.querySelector('.hero-logo');
        if (heroLogo) {
            animationElements.push({
                element: heroLogo,
                type: 'hero-logo',
                duration: '8s',
                name: 'heroFloat'
            });
        }
        
        // Floating images
        const floatingImage1 = document.querySelector('.floating-image-1');
        if (floatingImage1) {
            animationElements.push({
                element: floatingImage1,
                type: 'floating-1',
                duration: '60s',
                name: 'edgeOrbitCW'
            });
        }
        
        const floatingImage2 = document.querySelector('.floating-image-2');
        if (floatingImage2) {
            animationElements.push({
                element: floatingImage2,
                type: 'floating-2',
                duration: '60s',
                name: 'edgeOrbitCCW'
            });
        }
        
        // Decorative images
        const decorativeImages = document.querySelectorAll('.decorative-image');
        decorativeImages.forEach((img, index) => {
            animationElements.push({
                element: img,
                type: 'decorative',
                duration: '60s',
                name: index % 2 === 0 ? 'edgeOrbitCW' : 'edgeOrbitCCW'
            });
        });
        
        // Meteors
        const meteors = document.querySelectorAll('.meteor');
        meteors.forEach(meteor => {
            animationElements.push({
                element: meteor,
                type: 'meteor',
                duration: 'original', // Keep original duration
                name: 'shoot'
            });
        });
    }
    
    function storeOriginalStates() {
        animationElements.forEach(item => {
            const element = item.element;
            const computedStyle = window.getComputedStyle(element);
            
            originalStates.set(element, {
                animationName: computedStyle.animationName,
                animationDuration: computedStyle.animationDuration,
                animationTimingFunction: computedStyle.animationTimingFunction,
                animationIterationCount: computedStyle.animationIterationCount,
                animationDirection: computedStyle.animationDirection,
                animationFillMode: computedStyle.animationFillMode,
                animationPlayState: computedStyle.animationPlayState
            });
        });
    }
    
    function setupEventListeners() {
        // Page Visibility API
        document.addEventListener('visibilitychange', handleVisibilityChange);
        
        // Window focus/blur
        window.addEventListener('focus', handleFocus);
        window.addEventListener('blur', handleBlur);
        
        // Page show/hide
        window.addEventListener('pageshow', handlePageShow);
        window.addEventListener('pagehide', handlePageHide);
        
        // Before unload
        window.addEventListener('beforeunload', handleBeforeUnload);
    }
    
    function handleVisibilityChange() {
        if (document.hidden) {
            isPageVisible = false;
            pauseAnimations();
            console.log('Animation Fix: Page hidden - animations paused');
        } else {
            isPageVisible = true;
            resumeAnimations();
            console.log('Animation Fix: Page visible - animations resumed');
        }
    }
    
    function handleFocus() {
        isPageVisible = true;
        resumeAnimations();
    }
    
    function handleBlur() {
        isPageVisible = false;
        pauseAnimations();
    }
    
    function handlePageShow() {
        isPageVisible = true;
        setTimeout(() => {
            forceCorrectAnimations();
            resumeAnimations();
        }, 100);
    }
    
    function handlePageHide() {
        isPageVisible = false;
        pauseAnimations();
    }
    
    function handleBeforeUnload() {
        pauseAnimations();
    }
    
    function pauseAnimations() {
        animationElements.forEach(item => {
            const element = item.element;
            if (element && element.style) {
                element.style.animationPlayState = 'paused';
            }
        });
    }
    
    function resumeAnimations() {
        animationElements.forEach(item => {
            const element = item.element;
            if (element && element.style) {
                // Force correct properties before resuming
                forceElementAnimation(item);
                element.style.animationPlayState = 'running';
            }
        });
    }
    
    function forceCorrectAnimations() {
        animationElements.forEach(item => {
            forceElementAnimation(item);
        });
    }
    
    function forceElementAnimation(item) {
        const element = item.element;
        if (!element || !element.style) return;
        
        // Force correct animation properties
        switch (item.type) {
            case 'hero-logo':
                element.style.animationName = 'heroFloat';
                element.style.animationDuration = '8s';
                element.style.animationTimingFunction = 'ease-in-out';
                element.style.animationIterationCount = 'infinite';
                element.style.animationDirection = 'alternate';
                break;
                
            case 'floating-1':
                element.style.animationName = 'edgeOrbitCW';
                element.style.animationDuration = '60s';
                element.style.animationTimingFunction = 'linear';
                element.style.animationIterationCount = 'infinite';
                break;
                
            case 'floating-2':
                element.style.animationName = 'edgeOrbitCCW';
                element.style.animationDuration = '60s';
                element.style.animationTimingFunction = 'linear';
                element.style.animationIterationCount = 'infinite';
                break;
                
            case 'decorative':
                element.style.animationName = item.name;
                element.style.animationDuration = '60s';
                element.style.animationTimingFunction = 'linear';
                element.style.animationIterationCount = 'infinite';
                break;
        }
        
        element.style.animationFillMode = 'both';
    }
    
    // Periodic check to ensure animations are correct
    setInterval(() => {
        if (isPageVisible) {
            forceCorrectAnimations();
        }
    }, 5000);
    
    // Expose for debugging
    window.animationFix = {
        pause: pauseAnimations,
        resume: resumeAnimations,
        force: forceCorrectAnimations,
        elements: animationElements,
        isVisible: () => isPageVisible
    };
    
})();
