/* ===================================
   MOBILE NAVIGATION FUNCTIONALITY
   ===================================*/

document.addEventListener('DOMContentLoaded', function() {
    const hamburger = document.getElementById('hamburger-menu');
    const navLinks = document.getElementById('nav-links');
    const navLinksItems = navLinks.querySelectorAll('a');
    
    // Toggle mobile menu
    function toggleMobileMenu() {
        hamburger.classList.toggle('active');
        navLinks.classList.toggle('open');
        
        // Prevent body scroll when menu is open
        if (navLinks.classList.contains('open')) {
            document.body.style.overflow = 'hidden';
        } else {
            document.body.style.overflow = '';
        }
    }
    
    // Close mobile menu
    function closeMobileMenu() {
        hamburger.classList.remove('active');
        navLinks.classList.remove('open');
        document.body.style.overflow = '';
    }
    
    // Event listeners
    if (hamburger) {
        hamburger.addEventListener('click', toggleMobileMenu);
    }
    
    // Close menu when clicking on nav links
    navLinksItems.forEach(link => {
        link.addEventListener('click', closeMobileMenu);
    });
    
    // Close menu when clicking outside
    document.addEventListener('click', function(event) {
        const isClickInsideNav = navLinks.contains(event.target);
        const isClickOnHamburger = hamburger.contains(event.target);
        
        if (!isClickInsideNav && !isClickOnHamburger && navLinks.classList.contains('open')) {
            closeMobileMenu();
        }
    });
    
    // Close menu on window resize if screen becomes larger
    window.addEventListener('resize', function() {
        if (window.innerWidth > 767 && navLinks.classList.contains('open')) {
            closeMobileMenu();
        }
    });
    
    // Handle escape key
    document.addEventListener('keydown', function(event) {
        if (event.key === 'Escape' && navLinks.classList.contains('open')) {
            closeMobileMenu();
        }
    });
});

/* ===================================
   TOUCH GESTURES FOR MOBILE
   ===================================*/

// Add touch support for better mobile experience
document.addEventListener('DOMContentLoaded', function() {
    let touchStartY = 0;
    let touchEndY = 0;
    
    // Handle touch events for mobile navigation
    document.addEventListener('touchstart', function(e) {
        touchStartY = e.changedTouches[0].screenY;
    }, { passive: true });
    
    document.addEventListener('touchend', function(e) {
        touchEndY = e.changedTouches[0].screenY;
        handleSwipe();
    }, { passive: true });
    
    function handleSwipe() {
        const swipeThreshold = 50;
        const navLinks = document.getElementById('nav-links');
        
        if (touchEndY < touchStartY - swipeThreshold) {
            // Swipe up - close menu if open
            if (navLinks && navLinks.classList.contains('open')) {
                const hamburger = document.getElementById('hamburger-menu');
                hamburger.classList.remove('active');
                navLinks.classList.remove('open');
                document.body.style.overflow = '';
            }
        }
    }
});

/* ===================================
   SMOOTH SCROLLING ENHANCEMENT
   ===================================*/

document.addEventListener('DOMContentLoaded', function() {
    // Enhanced smooth scrolling for mobile
    const links = document.querySelectorAll('a[href^="#"]');
    
    links.forEach(link => {
        link.addEventListener('click', function(e) {
            e.preventDefault();
            
            const targetId = this.getAttribute('href');
            const targetSection = document.querySelector(targetId);
            
            if (targetSection) {
                const headerHeight = document.querySelector('.navbar').offsetHeight;
                const targetPosition = targetSection.offsetTop - headerHeight - 20;
                
                window.scrollTo({
                    top: targetPosition,
                    behavior: 'smooth'
                });
            }
        });
    });
});

/* ===================================
   MOBILE PERFORMANCE OPTIMIZATIONS
   ===================================*/

document.addEventListener('DOMContentLoaded', function() {
    // Optimize animations for mobile
    const isMobile = window.innerWidth <= 767;
    
    if (isMobile) {
        // Reduce animation complexity on mobile
        const style = document.createElement('style');
        style.textContent = `
            * {
                animation-duration: 0.3s !important;
                transition-duration: 0.3s !important;
            }
            
            .fade-in-element {
                animation-duration: 0.5s !important;
            }
        `;
        document.head.appendChild(style);
    }
    
    // Throttle scroll events for better performance
    let ticking = false;
    
    function updateScrollEffects() {
        // Your scroll effects here
        ticking = false;
    }
    
    function requestTick() {
        if (!ticking) {
            requestAnimationFrame(updateScrollEffects);
            ticking = true;
        }
    }
    
    window.addEventListener('scroll', requestTick, { passive: true });
});

/* ===================================
   ACCESSIBILITY IMPROVEMENTS
   ===================================*/

document.addEventListener('DOMContentLoaded', function() {
    // Add ARIA attributes for better accessibility
    const hamburger = document.getElementById('hamburger-menu');
    const navLinks = document.getElementById('nav-links');
    
    if (hamburger && navLinks) {
        hamburger.setAttribute('aria-expanded', 'false');
        hamburger.setAttribute('aria-controls', 'nav-links');
        navLinks.setAttribute('aria-labelledby', 'hamburger-menu');
        
        // Update ARIA attributes when menu toggles
        const observer = new MutationObserver(function(mutations) {
            mutations.forEach(function(mutation) {
                if (mutation.type === 'attributes' && mutation.attributeName === 'class') {
                    const isOpen = navLinks.classList.contains('open');
                    hamburger.setAttribute('aria-expanded', isOpen.toString());
                }
            });
        });
        
        observer.observe(navLinks, { attributes: true });
    }
    
    // Focus management for mobile menu
    hamburger?.addEventListener('click', function() {
        setTimeout(() => {
            if (navLinks.classList.contains('open')) {
                const firstLink = navLinks.querySelector('a');
                firstLink?.focus();
            }
        }, 100);
    });
});
