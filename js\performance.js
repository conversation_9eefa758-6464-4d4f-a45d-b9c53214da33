// تحسينات الأداء

document.addEventListener('DOMContentLoaded', () => {
    // تحسين تحميل الصور
    const images = document.querySelectorAll('img');
    images.forEach(img => {
        if (!img.src.includes('data:image')) {
            img.setAttribute('loading', 'lazy');
        }
    });

    // تحسين تحميل الخطوط
    const fontLinks = document.querySelectorAll('link[rel="stylesheet"][href$=".woff2"]').length;
    if (fontLinks > 0) {
        document.fonts.ready.then(() => {
            console.log('الخطوط تم تحميلها بنجاح');
        });
    }

    // تحسين إدارة الذاكرة - تم نقل هذا إلى animation-controller.js
    // لتجنب التضارب مع نظام التحكم في الأنيميشن الجديد

    // تحسين تحميل الفيديو
    const videos = document.querySelectorAll('video');
    videos.forEach(video => {
        video.setAttribute('preload', 'none');
        video.addEventListener('mouseenter', () => {
            video.setAttribute('preload', 'auto');
        });
    });
});
