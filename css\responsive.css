/* ===================================
   RESPONSIVE DESIGN - MOBILE FIRST
   ===================================*/

/* Base Mobile Styles (320px and up) */
* {
    box-sizing: border-box;
}

html {
    font-size: 16px;
    -webkit-text-size-adjust: 100%;
    -ms-text-size-adjust: 100%;
}

body {
    font-size: 1rem;
    line-height: 1.6;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    touch-action: manipulation;
    -webkit-tap-highlight-color: transparent;
    overscroll-behavior: contain;
}

/* Improved Touch Targets */
button, a, .clickable {
    min-height: 44px;
    min-width: 44px;
    touch-action: manipulation;
}

/* Mobile Navigation Improvements */
.navbar {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 1000;
    padding: 8px 15px;
    background: rgba(15, 15, 35, 0.95);
    backdrop-filter: blur(10px);
    transition: all 0.3s ease;
}

.nav-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    max-width: 1200px;
    margin: 0 auto;
    position: relative;
}

/* English page specific navbar layout */
html[lang="en"] .nav-container {
    flex-direction: row;
}

html[lang="en"] .logo-section {
    order: 1;
}

html[lang="en"] .nav-center {
    order: 2;
    margin-left: auto;
}

.logo-section {
    display: flex;
    gap: 10px;
    align-items: center;
}

.logo {
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.logo img {
    width: 100%;
    height: 100%;
    object-fit: contain;
}

/* Hamburger Menu */
.hamburger {
    display: none;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    width: 44px;
    height: 44px;
    background: transparent;
    border: none;
    cursor: pointer;
    padding: 8px;
    z-index: 1001;
}

.hamburger span {
    display: block;
    width: 25px;
    height: 3px;
    background: #fff;
    margin: 3px 0;
    transition: 0.3s;
    border-radius: 2px;
}

.hamburger.active span:nth-child(1) {
    transform: rotate(-45deg) translate(-5px, 6px);
}

.hamburger.active span:nth-child(2) {
    opacity: 0;
}

.hamburger.active span:nth-child(3) {
    transform: rotate(45deg) translate(-5px, -6px);
}

/* Mobile Navigation Menu */
.nav-links {
    display: flex;
    list-style: none;
    gap: 20px;
    margin: 0;
    padding: 0;
    align-items: center;
}

.nav-links a {
    color: #fff;
    text-decoration: none;
    padding: 10px 15px;
    border-radius: 25px;
    transition: all 0.3s ease;
    font-weight: 500;
}

.nav-links a:hover {
    color: var(--accent-color-1);
    transform: translateY(-2px);
}

/* Language Toggle */
#lang-toggle {
    background: var(--accent-color-1);
    color: #fff;
    border: none;
    padding: 8px 16px;
    border-radius: 20px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    margin-left: 15px;
}

#lang-toggle:hover {
    background: var(--accent-color-2);
    transform: scale(1.05);
}

/* Hero Section Mobile Improvements */
.hero-section {
    padding: 120px 20px 60px;
    text-align: center;
    min-height: 100vh;
    min-height: calc(var(--vh, 1vh) * 100);
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
}

.hero-logo {
    width: 120px;
    height: 120px;
    margin: 0 auto 30px;
}

.hero-logo img {
    width: 100%;
    height: 100%;
    object-fit: contain;
}

.hero-content {
    max-width: 600px;
    margin: 0 auto;
}

.hero-content p {
    font-size: 1.1rem;
    line-height: 1.8;
    margin-bottom: 30px;
    color: #e0e0e0;
}

.cta-button {
    display: inline-block;
    background: var(--accent-color-1);
    color: #fff;
    padding: 15px 30px;
    border-radius: 30px;
    text-decoration: none;
    font-weight: 600;
    transition: all 0.3s ease;
    border: none;
    cursor: pointer;
    font-size: 1rem;
    min-height: 44px;
}

.cta-button:hover {
    background: var(--accent-color-2);
    transform: translateY(-3px);
    box-shadow: 0 10px 25px rgba(247, 166, 0, 0.3);
}

/* Services Grid Mobile */
.services-grid {
    display: grid;
    grid-template-columns: 1fr;
    gap: 20px;
    padding: 0 20px;
    max-width: 1200px;
    margin: 0 auto;
}

.service-card {
    background: var(--card-bg);
    border-radius: 15px;
    padding: 25px;
    text-align: center;
    transition: all 0.3s ease;
    border: 1px solid rgba(255, 255, 255, 0.1);
    text-decoration: none;
    color: inherit;
    min-height: 200px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
}

.service-card:hover {
    transform: translateY(-5px);
    background: rgba(255, 255, 255, 0.08);
    border-color: var(--accent-color-1);
}

.service-card .img-service {
    width: 80px;
    height: 80px;
    object-fit: cover;
    border-radius: 10px;
    margin-bottom: 15px;
}

.service-card h3 {
    font-size: 1.2rem;
    margin: 0;
    color: var(--accent-color-1);
}

/* Contact Section Mobile */
.contact-grid {
    display: grid;
    grid-template-columns: 1fr;
    gap: 25px;
    padding: 0 20px;
}

.contact-item {
    text-align: center;
    padding: 20px;
    background: var(--card-bg);
    border-radius: 15px;
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.contact-item h4 {
    color: var(--accent-color-1);
    margin-bottom: 15px;
    font-size: 1.1rem;
}

.contact-item p {
    margin: 5px 0;
}

.contact-item a {
    color: #e0e0e0;
    text-decoration: none;
    transition: color 0.3s ease;
}

.contact-item a:hover {
    color: var(--accent-color-1);
}

/* Footer Mobile */
.footer {
    text-align: center;
    padding: 40px 20px;
    background: rgba(15, 15, 35, 0.8);
    margin-top: 60px;
}

.footer-content {
    max-width: 600px;
    margin: 0 auto;
}

.footer-logo {
    display: inline-block;
    width: 60px;
    height: 60px;
    margin-bottom: 20px;
}

.footer-logo img {
    width: 100%;
    height: 100%;
    object-fit: contain;
}

.footer-links {
    display: flex;
    justify-content: center;
    gap: 20px;
    margin-top: 20px;
    flex-wrap: wrap;
}

.footer-links a {
    color: #e0e0e0;
    text-decoration: none;
    padding: 8px 15px;
    border-radius: 20px;
    transition: all 0.3s ease;
}

.footer-links a:hover {
    background: var(--accent-color-1);
    color: #fff;
}

/* Scroll to Top Button */
.scroll-top {
    position: fixed;
    bottom: 30px;
    right: 30px;
    width: 50px;
    height: 50px;
    background: var(--accent-color-1);
    color: #fff;
    border: none;
    border-radius: 50%;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
    z-index: 1000;
}

.scroll-top.visible {
    opacity: 1;
    visibility: visible;
}

.scroll-top:hover {
    background: var(--accent-color-2);
    transform: translateY(-3px);
}

/* ===================================
   TABLET STYLES (768px and up)
   ===================================*/
@media (min-width: 768px) {
    .hero-section {
        padding: 140px 40px 80px;
    }

    .hero-logo {
        width: 150px;
        height: 150px;
    }

    .hero-content p {
        font-size: 1.2rem;
    }

    .services-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 30px;
        padding: 0 40px;
    }

    .contact-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 30px;
        padding: 0 40px;
    }

    .nav-links {
        gap: 25px;
    }

    .logo {
        width: 45px;
        height: 45px;
    }
}

/* ===================================
   DESKTOP STYLES (992px and up)
   ===================================*/
@media (min-width: 992px) {
    .hamburger {
        display: none !important;
    }

    .nav-links {
        display: flex !important;
        position: static !important;
        flex-direction: row !important;
        background: transparent !important;
        width: auto !important;
        box-shadow: none !important;
        padding: 0 !important;
        margin: 0 !important;
    }

    .hero-section {
        padding: 160px 60px 100px;
    }

    .hero-logo {
        width: 180px;
        height: 180px;
    }

    .services-grid {
        grid-template-columns: repeat(4, 1fr);
        gap: 40px;
        padding: 0 60px;
    }

    .contact-grid {
        grid-template-columns: repeat(3, 1fr);
        gap: 40px;
        padding: 0 60px;
    }

    .nav-center {
        display: flex;
        align-items: center;
        gap: 30px;
    }
}

/* ===================================
   LARGE DESKTOP STYLES (1200px and up)
   ===================================*/
@media (min-width: 1200px) {
    .hero-section {
        padding: 180px 80px 120px;
    }

    .hero-content p {
        font-size: 1.3rem;
    }

    .services-grid {
        padding: 0 80px;
        gap: 50px;
    }

    .contact-grid {
        padding: 0 80px;
        gap: 50px;
    }
}

/* ===================================
   MOBILE SPECIFIC STYLES (max-width: 767px)
   ===================================*/
@media (max-width: 767px) {
    .hamburger {
        display: flex !important;
    }

    .nav-links {
        display: none;
        position: fixed;
        top: 70px;
        left: 0;
        right: 0;
        flex-direction: column;
        background: rgba(15, 15, 35, 0.98);
        backdrop-filter: blur(20px);
        padding: 20px;
        margin: 0;
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
        z-index: 999;
    }

    /* English page mobile menu alignment */
    html[lang="en"] .nav-links {
        text-align: right;
        padding-right: 30px;
    }

    html[lang="en"] .nav-links li {
        text-align: right;
    }

    .nav-links.open {
        display: flex !important;
    }

    .nav-links li {
        width: 100%;
        text-align: center;
        margin: 5px 0;
    }

    .nav-links a {
        display: block;
        width: 100%;
        padding: 15px;
        border-radius: 10px;
    }

    #lang-toggle {
        margin: 15px auto 0;
        display: block;
        width: fit-content;
    }

    .hero-content p {
        font-size: 1rem;
        padding: 0 10px;
    }

    .service-card {
        padding: 20px;
        min-height: 180px;
    }

    .service-card .img-service {
        width: 60px;
        height: 60px;
    }

    .service-card h3 {
        font-size: 1.1rem;
    }

    .contact-item {
        padding: 15px;
    }

    .footer-links {
        flex-direction: column;
        gap: 10px;
    }
}

/* ===================================
   SMALL MOBILE STYLES (max-width: 480px)
   ===================================*/
@media (max-width: 480px) {
    .navbar {
        padding: 8px 10px;
    }

    .logo {
        width: 35px;
        height: 35px;
    }

    .hero-section {
        padding: 100px 15px 50px;
    }

    .hero-logo {
        width: 100px;
        height: 100px;
    }

    .hero-content p {
        font-size: 0.95rem;
        line-height: 1.6;
    }

    .cta-button {
        padding: 12px 25px;
        font-size: 0.95rem;
    }

    .services-grid {
        padding: 0 15px;
        gap: 15px;
    }

    .service-card {
        padding: 15px;
        min-height: 160px;
    }

    .service-card .img-service {
        width: 50px;
        height: 50px;
    }

    .service-card h3 {
        font-size: 1rem;
    }

    .contact-grid {
        padding: 0 15px;
        gap: 15px;
    }

    .contact-item {
        padding: 12px;
    }

    .contact-item h4 {
        font-size: 1rem;
    }

    .footer {
        padding: 30px 15px;
    }

    .scroll-top {
        bottom: 20px;
        right: 20px;
        width: 45px;
        height: 45px;
    }
}
