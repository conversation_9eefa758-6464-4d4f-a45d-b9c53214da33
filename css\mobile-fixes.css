/* ===================================
   MOBILE FIXES AND FINAL OPTIMIZATIONS
   ===================================*/

/* Fix for iOS Safari viewport issues */
@supports (-webkit-touch-callout: none) {
    .hero-section {
        min-height: -webkit-fill-available;
    }
}

/* Prevent horizontal scroll on mobile */
html, body {
    overflow-x: hidden;
    width: 100%;
    max-width: 100vw;
}

/* Fix for mobile keyboard appearing */
@media (max-width: 767px) {
    .hero-section {
        min-height: 100vh;
        min-height: calc(var(--vh, 1vh) * 100);
    }
    
    /* Ensure content doesn't get cut off */
    .main-content {
        padding-top: 70px; /* Account for fixed navbar */
    }
    
    /* Fix navbar positioning */
    .navbar {
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        z-index: 1000;
        transition: transform 0.3s ease;
    }
    
    /* Improve touch targets */
    .nav-links a,
    .cta-button,
    .service-card,
    .contact-item a,
    .footer-links a {
        min-height: 44px;
        min-width: 44px;
        display: flex;
        align-items: center;
        justify-content: center;
        touch-action: manipulation;
    }
    
    /* Fix slider on mobile */
    .slider-section {
        padding: 20px 0;
        overflow: hidden;
    }
    
    .slider {
        touch-action: pan-x;
        user-select: none;
        -webkit-user-select: none;
        -moz-user-select: none;
        -ms-user-select: none;
    }
    
    /* Improve vertical slider on mobile */
    .vertical-slider-section {
        padding: 40px 15px;
    }
    
    .vertical-content-container {
        flex-direction: column;
        gap: 30px;
    }
    
    .vertical-text-content {
        order: 2;
    }
    
    .vertical-slider-container {
        order: 1;
    }
    
    /* Fix contact section spacing */
    .contact-section {
        margin-top: 40px;
    }
    
    /* Improve footer on mobile */
    .footer {
        margin-top: 40px;
    }
    
    /* Fix any overflow issues */
    .section {
        overflow-x: hidden;
    }
    
    /* Improve loading screen on mobile */
    .page-loader {
        position: fixed;
        top: 0;
        left: 0;
        width: 100vw;
        height: 100vh;
        height: calc(var(--vh, 1vh) * 100);
        z-index: 9999;
    }
}

/* Small mobile specific fixes */
@media (max-width: 480px) {
    /* Reduce padding for very small screens */
    .hero-section {
        padding: 100px 10px 40px;
    }
    
    .services-section {
        padding: 40px 10px;
    }
    
    .contact-section {
        padding: 40px 10px;
    }
    
    .footer {
        padding: 30px 10px 20px;
    }
    
    /* Ensure text is readable */
    .hero-content p {
        font-size: 0.9rem;
        line-height: 1.5;
    }
    
    .section-title {
        font-size: 1.8rem;
    }
    
    /* Fix slider for very small screens */
    .slider {
        height: 250px;
        margin: 20px auto;
    }
    
    .slide img {
        height: 200px;
    }
}

/* Landscape orientation fixes for mobile */
@media (max-width: 767px) and (orientation: landscape) {
    .hero-section {
        min-height: 100vh;
        padding: 80px 20px 40px;
    }
    
    .hero-logo {
        width: 80px;
        height: 80px;
        margin-bottom: 20px;
    }
    
    .hero-content p {
        font-size: 0.95rem;
        margin-bottom: 20px;
    }
    
    .slider {
        height: 200px;
        margin: 20px auto;
    }
    
    .slide img {
        height: 150px;
    }
}

/* Fix for devices with notch */
@supports (padding: max(0px)) {
    .navbar {
        padding-left: max(15px, env(safe-area-inset-left));
        padding-right: max(15px, env(safe-area-inset-right));
    }
    
    .hero-section,
    .services-section,
    .contact-section,
    .footer {
        padding-left: max(20px, env(safe-area-inset-left));
        padding-right: max(20px, env(safe-area-inset-right));
    }
}

/* Performance optimizations for mobile */
@media (max-width: 767px) {
    /* Reduce motion for better performance */
    .floating-particles,
    .meteor {
        display: none !important;
    }
    
    /* Simplify backgrounds */
    .animated-bg {
        background: linear-gradient(135deg, #0f0f23, #1a1a2e) !important;
    }
    
    /* Optimize transforms */
    .service-card,
    .contact-item,
    .slide {
        will-change: transform;
        backface-visibility: hidden;
        -webkit-backface-visibility: hidden;
    }
    
    /* Improve scroll performance */
    .main-content {
        transform: translateZ(0);
        -webkit-transform: translateZ(0);
    }
}

/* Fix for iOS bounce scroll */
body {
    -webkit-overflow-scrolling: touch;
    overscroll-behavior-y: contain;
}

/* Prevent zoom on input focus (if any inputs are added later) */
input, select, textarea {
    font-size: 16px !important;
}

/* Fix for Chrome mobile address bar */
@media screen and (max-width: 767px) {
    .hero-section {
        min-height: 100vh;
        min-height: calc(var(--vh, 1vh) * 100);
    }
}

/* Accessibility improvements for mobile */
@media (max-width: 767px) {
    /* Improve focus indicators */
    button:focus,
    a:focus,
    .service-card:focus {
        outline: 2px solid var(--accent-color-1);
        outline-offset: 2px;
    }
    
    /* Improve contrast for small screens */
    .hero-content p,
    .contact-item p {
        color: #f0f0f0;
    }
    
    /* Ensure sufficient color contrast */
    .service-card h3 {
        color: var(--accent-color-1);
        font-weight: 600;
    }
}

/* Fix for Android Chrome */
@media screen and (max-width: 767px) {
    .navbar {
        position: fixed;
        top: 0;
        transform: translateZ(0);
        -webkit-transform: translateZ(0);
    }
    
    /* Prevent content jumping */
    .main-content {
        padding-top: 70px;
    }
}

/* Print styles (hide unnecessary elements) */
@media print {
    .navbar,
    .scroll-top,
    .floating-particles,
    .meteor,
    .animated-bg {
        display: none !important;
    }
    
    .main-content {
        padding-top: 0 !important;
    }
}

/* High DPI display optimizations */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
    .hero-logo img,
    .logo img,
    .footer-logo img {
        image-rendering: -webkit-optimize-contrast;
        image-rendering: crisp-edges;
    }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
    :root {
        --text-color: #ffffff;
        --bg-dark: #0a0a0a;
        --card-bg: rgba(255, 255, 255, 0.03);
    }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
    *,
    *::before,
    *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
        scroll-behavior: auto !important;
    }
    
    .fade-in-element {
        opacity: 1 !important;
        transform: none !important;
    }
}
