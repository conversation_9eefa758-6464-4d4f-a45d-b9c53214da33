/* ===================================
   VERTICAL SLIDER MOBILE OPTIMIZATION
   ===================================*/

/* Base Vertical Slider Styles */
.vertical-slider-section {
    padding: 60px 20px;
    position: relative;
    overflow: hidden;
}

.decorative-image {
    position: absolute;
    z-index: 1;
    opacity: 0.7;
    transition: transform 0.3s ease;
}

.floating-image-1 {
    top: 10%;
    left: 5%;
    animation: floatAnimation1 6s ease-in-out infinite;
}

.floating-image-2 {
    bottom: 10%;
    right: 5%;
    animation: floatAnimation2 6s ease-in-out infinite;
}

.decorative-image img {
    width: 80px;
    height: 80px;
    object-fit: contain;
    filter: drop-shadow(0 5px 15px rgba(247, 166, 0, 0.3));
}

@keyframes floatAnimation1 {
    0%, 100% {
        transform: translateY(0px) rotate(0deg);
    }
    50% {
        transform: translateY(-20px) rotate(180deg);
    }
}

@keyframes floatAnimation2 {
    0%, 100% {
        transform: translateY(0px) rotate(0deg);
    }
    50% {
        transform: translateY(-20px) rotate(-180deg);
    }
}

.vertical-content-container {
    display: flex;
    align-items: center;
    gap: 50px;
    max-width: 1200px;
    margin: 0 auto;
    position: relative;
    z-index: 2;
}

.vertical-text-content {
    flex: 1;
    padding-right: 30px;
}

.text-slide {
    opacity: 0;
    transform: translateY(20px);
    transition: all 0.5s ease;
    display: none;
}

.text-slide.active {
    opacity: 1;
    transform: translateY(0);
    display: block;
}

.text-slide h3 {
    color: var(--accent-color-1);
    font-size: 1.8rem;
    margin-bottom: 20px;
    font-weight: 600;
}

.text-slide p {
    color: #e0e0e0;
    font-size: 1.1rem;
    line-height: 1.8;
}

.vertical-slider-container {
    flex: 1;
    padding-left: 30px;
}

.gallery-container {
    position: relative;
    width: 100%;
    height: 400px;
    overflow: hidden;
    border-radius: 20px;
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.3);
}

.gallery-wrapper {
    width: 100%;
    height: 100%;
}

.gallery-grid {
    width: 100%;
    height: 100%;
    position: relative;
}

.image-card {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    opacity: 0;
    transition: opacity 0.5s ease;
    border-radius: 20px;
    overflow: hidden;
}

.image-card.active {
    opacity: 1;
}

.image-card img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.5s ease;
}

.image-card:hover img {
    transform: scale(1.05);
}

/* Mobile Android Specific Styles */
@media (max-width: 767px) {
    .vertical-slider-section {
        padding: 40px 15px;
    }
    
    .decorative-image {
        display: none; /* Hide floating images on mobile */
    }
    
    .vertical-content-container {
        flex-direction: column;
        gap: 30px;
        text-align: center;
    }
    
    .vertical-text-content {
        order: 2;
        padding-right: 0;
        width: 100%;
    }
    
    .vertical-slider-container {
        order: 1;
        padding-left: 0;
        width: 100%;
        max-width: 350px;
        margin: 0 auto;
    }
    
    .gallery-container {
        height: 250px;
        border-radius: 15px;
    }
    
    .text-slide h3 {
        font-size: 1.4rem;
        margin-bottom: 15px;
    }
    
    .text-slide p {
        font-size: 1rem;
        line-height: 1.6;
    }
    
    /* Android specific adjustments */
    @supports (-webkit-appearance: none) {
        .gallery-container {
            height: 220px;
        }
        
        .text-slide h3 {
            font-size: 1.3rem;
        }
        
        .text-slide p {
            font-size: 0.95rem;
        }
    }
}

/* Tablet Styles */
@media (min-width: 768px) and (max-width: 991px) {
    .vertical-slider-section {
        padding: 50px 30px;
    }
    
    .decorative-image img {
        width: 60px;
        height: 60px;
    }
    
    .vertical-content-container {
        gap: 40px;
    }
    
    .gallery-container {
        height: 350px;
    }
    
    .text-slide h3 {
        font-size: 1.6rem;
    }
    
    .text-slide p {
        font-size: 1.05rem;
    }
}

/* Desktop Styles */
@media (min-width: 992px) {
    .vertical-slider-section {
        padding: 80px 60px;
    }
    
    .decorative-image img {
        width: 100px;
        height: 100px;
    }
    
    .floating-image-1 {
        animation-duration: 8s;
    }
    
    .floating-image-2 {
        animation-duration: 8s;
    }
    
    .gallery-container {
        height: 450px;
    }
    
    .text-slide h3 {
        font-size: 2rem;
    }
    
    .text-slide p {
        font-size: 1.2rem;
    }
}

/* Large Desktop */
@media (min-width: 1200px) {
    .vertical-slider-section {
        padding: 100px 80px;
    }
    
    .decorative-image img {
        width: 120px;
        height: 120px;
    }
    
    .vertical-content-container {
        gap: 60px;
    }
    
    .gallery-container {
        height: 500px;
    }
}

/* iPad specific adjustments */
@media (min-width: 768px) and (max-width: 1024px) and (orientation: portrait) {
    .vertical-content-container {
        flex-direction: column;
        gap: 40px;
        text-align: center;
    }
    
    .vertical-text-content {
        order: 2;
        padding-right: 0;
    }
    
    .vertical-slider-container {
        order: 1;
        padding-left: 0;
        max-width: 400px;
        margin: 0 auto;
    }
    
    .gallery-container {
        height: 300px;
    }
}

/* Performance optimizations for mobile */
@media (max-width: 767px) {
    .decorative-image {
        will-change: transform;
    }
    
    .image-card img {
        will-change: transform;
    }
    
    .text-slide {
        will-change: opacity, transform;
    }
}
