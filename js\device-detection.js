/* ===================================
   DEVICE DETECTION AND ADAPTATION
   ===================================*/

document.addEventListener('DOMContentLoaded', function() {
    // Device detection
    const userAgent = navigator.userAgent.toLowerCase();
    const isAndroid = /android/.test(userAgent);
    const isIOS = /iphone|ipad|ipod/.test(userAgent);
    const isTablet = /ipad/.test(userAgent) || (isAndroid && !/mobile/.test(userAgent));
    const isMobile = /mobile/.test(userAgent) && !isTablet;
    const isDesktop = !isMobile && !isTablet;
    
    // Add device classes to body
    document.body.classList.add(
        isAndroid ? 'android-device' : '',
        isIOS ? 'ios-device' : '',
        isTablet ? 'tablet-device' : '',
        isMobile ? 'mobile-device' : '',
        isDesktop ? 'desktop-device' : ''
    );
    
    // Device-specific optimizations
    if (isAndroid) {
        optimizeForAndroid();
    }
    
    if (isIOS) {
        optimizeForIOS();
    }
    
    if (isTablet) {
        optimizeForTablet();
    }
    
    // Initialize responsive features
    initResponsiveFeatures();
    
    // Handle orientation changes
    handleOrientationChange();
});

/* ===================================
   ANDROID OPTIMIZATIONS
   ===================================*/
function optimizeForAndroid() {
    // Android-specific navbar adjustments
    const navbar = document.querySelector('.navbar');
    if (navbar) {
        navbar.style.height = '60px';
        navbar.style.backdropFilter = 'blur(10px)';
    }
    
    // Android-specific slider adjustments
    const slider = document.querySelector('.slider');
    if (slider) {
        slider.style.height = '240px';
        slider.style.margin = '20px auto';
    }
    
    // Android-specific vertical slider
    const verticalSlider = document.querySelector('.vertical-slider-section');
    if (verticalSlider) {
        verticalSlider.style.padding = '30px 10px';
        
        // Hide floating images on Android
        const floatingImages = document.querySelectorAll('.decorative-image');
        floatingImages.forEach(img => {
            img.style.display = 'none';
        });
    }
    
    // Optimize touch events for Android
    optimizeAndroidTouch();
}

function optimizeAndroidTouch() {
    // Improve touch responsiveness
    const touchElements = document.querySelectorAll('button, .service-card, .nav-links a, .cta-button');
    
    touchElements.forEach(element => {
        element.style.touchAction = 'manipulation';
        element.style.webkitTapHighlightColor = 'transparent';
        
        // Add Android-specific touch feedback
        element.addEventListener('touchstart', function() {
            this.style.transform = 'scale(0.95)';
            this.style.opacity = '0.8';
        }, { passive: true });
        
        element.addEventListener('touchend', function() {
            setTimeout(() => {
                this.style.transform = '';
                this.style.opacity = '';
            }, 150);
        }, { passive: true });
    });
}

/* ===================================
   IOS OPTIMIZATIONS
   ===================================*/
function optimizeForIOS() {
    // iOS-specific viewport fixes
    const viewport = document.querySelector('meta[name="viewport"]');
    if (viewport) {
        viewport.setAttribute('content', 
            'width=device-width, initial-scale=1.0, maximum-scale=5.0, user-scalable=yes, viewport-fit=cover'
        );
    }
    
    // iOS Safari specific fixes
    document.body.style.webkitOverflowScrolling = 'touch';
    document.body.style.overscrollBehavior = 'contain';
    
    // Fix iOS Safari bottom bar issue
    const setIOSHeight = () => {
        const vh = window.innerHeight * 0.01;
        document.documentElement.style.setProperty('--vh', `${vh}px`);
    };
    
    setIOSHeight();
    window.addEventListener('resize', setIOSHeight);
    window.addEventListener('orientationchange', setIOSHeight);
}

/* ===================================
   TABLET OPTIMIZATIONS
   ===================================*/
function optimizeForTablet() {
    // Tablet-specific layout adjustments
    const servicesGrid = document.querySelector('.services-grid');
    if (servicesGrid) {
        servicesGrid.style.maxWidth = '800px';
    }
    
    // Tablet-specific navbar
    const navbar = document.querySelector('.navbar');
    if (navbar) {
        navbar.style.padding = '12px 30px';
    }
    
    // Tablet-specific slider
    const slider = document.querySelector('.slider');
    if (slider) {
        slider.style.height = '350px';
        slider.style.width = '90%';
    }
}

/* ===================================
   RESPONSIVE FEATURES
   ===================================*/
function initResponsiveFeatures() {
    // Responsive images
    const images = document.querySelectorAll('img');
    images.forEach(img => {
        img.style.maxWidth = '100%';
        img.style.height = 'auto';
    });
    
    // Responsive containers
    const containers = document.querySelectorAll('.container, .nav-container, .footer-content');
    containers.forEach(container => {
        container.style.maxWidth = '100%';
        container.style.padding = '0 15px';
    });
    
    // Responsive text scaling
    adjustTextSizes();
    
    // Initialize responsive navigation
    initResponsiveNavigation();
}

function adjustTextSizes() {
    const screenWidth = window.innerWidth;
    const root = document.documentElement;
    
    if (screenWidth < 480) {
        root.style.fontSize = '14px';
    } else if (screenWidth < 768) {
        root.style.fontSize = '15px';
    } else if (screenWidth < 992) {
        root.style.fontSize = '16px';
    } else {
        root.style.fontSize = '16px';
    }
}

function initResponsiveNavigation() {
    const hamburger = document.querySelector('.hamburger');
    const navLinks = document.querySelector('.nav-links');
    
    if (hamburger && navLinks) {
        // Ensure proper mobile navigation behavior
        hamburger.addEventListener('click', function() {
            const isOpen = navLinks.classList.contains('open');
            
            if (isOpen) {
                navLinks.classList.remove('open');
                hamburger.classList.remove('active');
                document.body.style.overflow = '';
            } else {
                navLinks.classList.add('open');
                hamburger.classList.add('active');
                document.body.style.overflow = 'hidden';
            }
        });
        
        // Close menu when clicking outside
        document.addEventListener('click', function(e) {
            if (!hamburger.contains(e.target) && !navLinks.contains(e.target)) {
                navLinks.classList.remove('open');
                hamburger.classList.remove('active');
                document.body.style.overflow = '';
            }
        });
    }
}

/* ===================================
   ORIENTATION HANDLING
   ===================================*/
function handleOrientationChange() {
    window.addEventListener('orientationchange', function() {
        setTimeout(() => {
            // Recalculate viewport height
            const vh = window.innerHeight * 0.01;
            document.documentElement.style.setProperty('--vh', `${vh}px`);
            
            // Adjust layout based on orientation
            const isLandscape = window.orientation === 90 || window.orientation === -90;
            
            if (isLandscape && window.innerHeight < 500) {
                // Landscape mobile adjustments
                const heroSection = document.querySelector('.hero-section');
                if (heroSection) {
                    heroSection.style.padding = '60px 20px 40px';
                }
                
                const slider = document.querySelector('.slider');
                if (slider) {
                    slider.style.height = '200px';
                }
            }
            
            // Re-initialize responsive features
            adjustTextSizes();
        }, 100);
    });
}

/* ===================================
   PERFORMANCE MONITORING
   ===================================*/
function monitorDevicePerformance() {
    // Monitor frame rate
    let lastTime = performance.now();
    let frameCount = 0;
    
    function checkFrameRate() {
        const currentTime = performance.now();
        frameCount++;
        
        if (currentTime - lastTime >= 1000) {
            const fps = frameCount;
            frameCount = 0;
            lastTime = currentTime;
            
            // Reduce animations if low FPS
            if (fps < 30) {
                document.body.classList.add('low-performance');
                reduceAnimations();
            }
        }
        
        requestAnimationFrame(checkFrameRate);
    }
    
    requestAnimationFrame(checkFrameRate);
}

function reduceAnimations() {
    const style = document.createElement('style');
    style.textContent = `
        .low-performance * {
            animation-duration: 0.1s !important;
            transition-duration: 0.1s !important;
        }
        
        .low-performance .floating-particles,
        .low-performance .meteor,
        .low-performance .decorative-image {
            display: none !important;
        }
    `;
    document.head.appendChild(style);
}

// Initialize performance monitoring
monitorDevicePerformance();
