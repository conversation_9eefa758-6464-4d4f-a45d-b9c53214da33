/* ===================================
   ANIMATION FIXES - PREVENT SPEED ISSUES
   CRITICAL: DO NOT MODIFY THESE RULES
   ===================================*/

/* ABSOLUTE PROTECTION - HIGHEST PRIORITY */
.hero-logo {
    animation: heroFloat 8s ease-in-out infinite alternate !important;
}

.floating-image-1 {
    animation: edgeOrbitCW 60s linear infinite !important;
}

.floating-image-2 {
    animation: edgeOrbitCCW 60s linear infinite !important;
}

.decorative-image:nth-child(odd) {
    animation: edgeOrbitCW 60s linear infinite !important;
}

.decorative-image:nth-child(even) {
    animation: edgeOrbitCCW 60s linear infinite !important;
}

/* Ensure animations maintain consistent timing - DO NOT MODIFY */
.hero-logo,
.floating-image-1,
.floating-image-2,
.decorative-image {
    animation-fill-mode: both !important;
}

/* Prevent mobile performance optimizations from breaking animations */
@media (max-width: 767px) {
    .hero-logo {
        animation-duration: 8s !important;
        animation-timing-function: ease-in-out !important;
        animation-iteration-count: infinite !important;
    }

    .floating-image-1, .floating-image-2, .decorative-image {
        animation-duration: 60s !important;
        animation-timing-function: linear !important;
        animation-iteration-count: infinite !important;
    }

    .meteor {
        animation-timing-function: linear !important;
        animation-iteration-count: infinite !important;
    }
}

/* Hero logo animation fix */
.hero-logo {
    animation-name: heroFloat;
    animation-duration: 8s;
    animation-timing-function: ease-in-out;
    animation-iteration-count: infinite;
    animation-direction: alternate;
    animation-fill-mode: both;
    animation-play-state: running;
}

/* Floating images animation fix */
.floating-image-1,
.decorative-image:first-child {
    animation-name: edgeOrbitCW;
    animation-duration: 60s;
    animation-timing-function: linear;
    animation-iteration-count: infinite;
    animation-fill-mode: both;
    animation-play-state: running;
}

.floating-image-2,
.decorative-image:last-child {
    animation-name: edgeOrbitCCW;
    animation-duration: 60s;
    animation-timing-function: linear;
    animation-iteration-count: infinite;
    animation-fill-mode: both;
    animation-play-state: running;
}

/* Meteor animation fix */
.meteor {
    animation-timing-function: linear;
    animation-fill-mode: both;
    animation-play-state: running;
}

/* Floating particles fix */
.floating-particles,
.particle {
    animation-timing-function: ease-in-out;
    animation-fill-mode: both;
    animation-play-state: running;
}

/* Page visibility animation control */
.page-hidden * {
    animation-play-state: paused !important;
}

.page-visible * {
    animation-play-state: running !important;
}

/* Tab inactive state */
.tab-inactive {
    animation-play-state: paused !important;
}

.tab-active {
    animation-play-state: running !important;
}

/* Force specific animation durations to prevent speed issues */
.hero-logo {
    animation-name: heroFloat !important;
    animation-duration: 8s !important;
    animation-timing-function: ease-in-out !important;
    animation-iteration-count: infinite !important;
    animation-direction: alternate !important;
}

.floating-image-1 {
    animation-name: edgeOrbitCW !important;
    animation-duration: 60s !important;
    animation-timing-function: linear !important;
    animation-iteration-count: infinite !important;
}

.floating-image-2 {
    animation-name: edgeOrbitCCW !important;
    animation-duration: 60s !important;
    animation-timing-function: linear !important;
    animation-iteration-count: infinite !important;
}

/* Prevent animation speed issues during tab switching */
@media (prefers-reduced-motion: no-preference) {
    .hero-logo,
    .floating-image-1,
    .floating-image-2,
    .decorative-image,
    .meteor,
    .floating-particles,
    .particle {
        animation-play-state: running;
        will-change: transform;
        backface-visibility: hidden;
        -webkit-backface-visibility: hidden;
    }
}

/* Pause animations when page is hidden */
:root[data-page-hidden="true"] * {
    animation-play-state: paused !important;
}

:root[data-page-visible="true"] * {
    animation-play-state: running !important;
}

/* Specific fixes for common animated elements */
.fade-in-element {
    animation-fill-mode: both;
    animation-play-state: running;
}

.slide-in-element {
    animation-fill-mode: both;
    animation-play-state: running;
}

/* Fix for CSS transforms during tab switching */
.animated-element {
    transform-style: preserve-3d;
    backface-visibility: hidden;
    -webkit-backface-visibility: hidden;
}

/* Ensure smooth animation resumption */
.animation-resuming {
    animation-play-state: running !important;
    transition: none !important;
}

.animation-pausing {
    animation-play-state: paused !important;
}

/* Performance optimizations */
.optimized-animation {
    will-change: transform, opacity;
    contain: layout style paint;
}

/* Browser-specific fixes */
@supports (-webkit-appearance: none) {
    /* Safari/Chrome specific fixes */
    .hero-logo,
    .floating-image-1,
    .floating-image-2 {
        -webkit-animation-fill-mode: both;
        -webkit-animation-play-state: running;
    }
}

@supports (-moz-appearance: none) {
    /* Firefox specific fixes */
    .hero-logo,
    .floating-image-1,
    .floating-image-2 {
        -moz-animation-fill-mode: both;
        -moz-animation-play-state: running;
    }
}

/* Fallback for older browsers */
@media screen and (-ms-high-contrast: active), (-ms-high-contrast: none) {
    /* IE/Edge specific fixes */
    .hero-logo,
    .floating-image-1,
    .floating-image-2 {
        animation-play-state: running;
    }
}

/* Debug mode - uncomment to see animation states */
/*
.animation-debug * {
    outline: 1px solid red;
}

.animation-debug .animation-paused {
    outline: 3px solid orange;
}

.animation-debug .animation-running {
    outline: 3px solid green;
}
*/

/* Responsive animation adjustments */
@media (max-width: 767px) {
    /* Mobile optimizations */
    .hero-logo {
        animation-duration: 6s; /* Slightly faster on mobile for better UX */
    }
    
    .floating-image-1,
    .floating-image-2 {
        animation-duration: 45s; /* Slightly faster on mobile */
    }
}

@media (max-width: 480px) {
    /* Very small screens */
    .hero-logo {
        animation-duration: 5s;
    }
    
    .floating-image-1,
    .floating-image-2 {
        animation-duration: 40s;
    }
}

/* High performance mode for low-end devices */
@media (max-width: 480px) and (max-height: 800px) {
    .performance-mode * {
        animation-duration: 0.5s !important;
        animation-iteration-count: 1 !important;
    }
}

/* Accessibility - respect user preferences */
@media (prefers-reduced-motion: reduce) {
    .hero-logo,
    .floating-image-1,
    .floating-image-2,
    .decorative-image,
    .meteor,
    .floating-particles {
        animation: none !important;
        transform: none !important;
    }
}

/* Print styles - disable animations */
@media print {
    * {
        animation: none !important;
        transition: none !important;
        transform: none !important;
    }
}
