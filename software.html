<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تطوير البرمجيات - MCT</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="css/style.css">
    <link rel="stylesheet" href="css/responsive.css">
    <link rel="stylesheet" href="css/device-adaptive.css">
    <link rel="stylesheet" href="css/service-pages-navbar.css">
    <link rel="stylesheet" href="css/mobile-fixes.css">
    <style>
        /* Font Application for Software Page */
        * {
            font-family: 'NotoNaskhArabic', 'Arial', sans-serif;
        }
        
        body {
            font-family: 'NotoNaskhArabic', 'Arial', sans-serif;
        }
        
        h1, h2, h3, h4, h5, h6 {
            font-family: 'NotoNaskhArabic', 'Arial', sans-serif;
            font-weight: 700;
        }
        
        .cta-button, button {
            font-family: 'NotoNaskhArabic', 'Arial', sans-serif;
            font-weight: 600;
        }
        
        .nav-links a {
            font-family: 'NotoNaskhArabic', 'Arial', sans-serif;
            font-weight: 500;
        }
    </style>
</head>
<body class="service-page">
    <!-- شاشة التحميل -->
    <div class="page-loader" id="pageLoader">
        <div class="loader-logo">
            <img src="image/logo-8.png" alt="MCT Logo">
            <div class="loader-text">Software Development</div>
        </div>
    </div>

    <div class="animated-bg"></div>
    <div class="floating-particles" id="particles"></div>
    <nav class="navbar">
        <div class="nav-container">
            <div class="logo-section">
                <a href="index.html" class="logo">
                    <img src="image/logo-11.png" alt="MCT Logo">
                </a>
                <a href="index.html" class="logo">
                    <img src="image/logo-10.png" alt="MCT Logo">
                </a>
            </div>
            <div class="nav-center">
                <button class="hamburger" id="hamburger-menu" aria-label="Open menu">
                    <span></span>
                    <span></span>
                    <span></span>
                </button>
                <ul class="nav-links" id="nav-links">
                    <li><a href="networking.html">حلول الشبكات</a></li>
                    <li><a href="software.html" class="active">تطوير البرمجيات</a></li>
                    <li><a href="cybersecurity.html">أمن المعلومات</a></li>
                    <li><a href="maintenance.html">الدعم والصيانة</a></li>
                    <li><a href="index.html">العودة للرئيسية</a></li>
                    <li><button id="lang-toggle" onclick="switchLanguage('en')">EN</button></li>
                </ul>
            </div>
        </div>
    </nav>
    <main class="main-content">
        <!-- Hero Section -->
        <section class="service-hero fade-in-element">
            <div class="service-icon"><i class="fas fa-code"></i></div>
            <h1 class="service-title fade-in-element delay-1">تطوير البرمجيات</h1>
            <p class="service-subtitle fade-in-element delay-2">
                نقدم حلول برمجية متكاملة ومخصصة تلبي احتياجات أعمالكم. فريقنا من المطورين المحترفين يضمن تقديم برمجيات عالية الجودة وأداء متميز.
            </p>
        </section>

        <!-- خدماتنا -->
        <section class="content-section fade-in-element delay-3">
            <h2 class="section-title fade-in-element">خدماتنا في تطوير البرمجيات</h2>
            <div class="services-with-animation">
                <div class="services-text">
                    <ul class="services-list fade-in-element delay-1">
                        <li><strong>تطوير تطبيقات الويب: </strong>نحن نقدم خدمات تطوير تطبيقات الويب باستخدام أحدث التقنيات مثل HTML5، CSS3، JavaScript، وFrameworks مثل React وAngular. تشمل خدماتنا:<br>•	تطوير مواقع التجارة الإلكترونية: تصميم وتطوير منصات تسوق إلكترونية متكاملة.
<br>•	تطبيقات الويب التفاعلية: إنشاء تطبيقات تفاعلية تسهل تجربة المستخدم.
<br>•	إنشاء مواقع شخصية.
</li>
                        <li><strong>برمجة تطبيقات ويندوز: </strong> نحن نعمل على تطوير تطبيقات ويندوز باستخدام تقنيات مثل .NET وC#. تشمل خدماتنا:<br>•	تطبيقات سطح المكتب: تصميم تطبيقات قوية وسهلة الاستخدام.
<br>•	تطبيقات الأعمال: حلول مخصصة تلبي احتياجات الشركات.
</li>
                        <li><strong>برمجة تطبيقات أندرويد: </strong>نقوم بعمل تطبيقات موبايل احترافية توفر تجربة عملية للمستخدم بكافة المجالات.</li>
                        <li><strong>صيانة وتطوير البرمجيات: </strong> تحديث وتحسين البرمجيات الموجودة.</li>
                        <li><strong>تطوير البرمجيات المخصصة: </strong> تصميم برمجيات تلبي احتياجات محددة.</li>
                        <li><strong>أدوات إدارة الأعمال: </strong>تطوير أدوات تساعد في تحسين كفاءة العمل.</li>
                    </ul>
                </div>
                <div class="services-animation">
                    <div class="dashboard-container">
                        <div class="rotating-images">
                            <div class="image-orbit">
                                <!-- Top -->
                                <img src="image/file_11634597.png" alt="File Icon" class="rotating-image" style="top: -5%; left: 50%; transform: translate(-50%, -50%);">
                                <!-- Top Right -->
                                <img src="image/document_11665380.png" alt="Document Icon" class="rotating-image" style="top: 20%; right: 0; transform: translate(50%, -50%);">
                                <!-- Bottom Right -->
                                <img src="image/c-sharp_6132221.png" alt="C# Icon" class="rotating-image" style="bottom: 20%; right: 0; transform: translate(50%, 50%);">
                                <!-- Bottom -->
                                <img src="image/web-coding_11513813.png" alt="Web Coding Icon" class="rotating-image" style="bottom: -3%; left: 50%; transform: translate(-50%, 50%);">
                                <!-- Bottom Left -->
                                <img src="image/code_4997543.png" alt="Code Icon" class="rotating-image" style="bottom: 20%; left: 0; transform: translate(-50%, 50%);">
                                <!-- Top Left -->
                                <img src="image/java_3291669.png" alt="Java Icon" class="rotating-image" style="top: 20%; left: 0; transform: translate(-50%, -50%);">
                            </div>
                        </div>
                        <!-- Main Laptop -->
                        <div class="laptop">
                            <div class="laptop-screen">
                                <div class="screen-content">
                                    <div class="window-bar">
                                        <div class="window-buttons">
                                            <div class="window-button"></div>
                                            <div class="window-button"></div>
                                            <div class="window-button"></div>
                                        </div>
                                    </div>
                                    <div class="content-area">
                                        <div class="left-panel">
                                            <div class="blue-section"></div>
                                            <div class="green-section"></div>
                                        </div>
                                        <div class="right-panel">
                                            <div class="data-lines">
                                                <div class="line"></div>
                                                <div class="line"></div>
                                                <div class="line"></div>
                                                <div class="line"></div>
                                                <div class="line"></div>
                                                <div class="line"></div>
                                                <div class="line"></div>
                                                <div class="line"></div>
                                                <div class="line"></div>
                                                <div class="line"></div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <!-- Connection Lines -->
                        <div class="connection-lines">
                            <div class="connection-line line-1"></div>
                            <div class="connection-line line-2"></div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <section class="content-section fade-in-element delay-4">
            <h2 class="section-title fade-in-element" style="margin-top: 50px;">لماذا تختار خدماتنا؟</h2>
            <div class="features-grid">
                <div class="feature-card fade-in-element delay-1">
                    <h3>خبرة واسعة</h3>
                    <p>فريق من المطورين المحترفين ذوي الخبرة في مختلف التقنيات والأدوات</p>
                </div>
                <div class="feature-card fade-in-element delay-2">
                    <h3>حلول مخصصة</h3>
                    <p>تطوير برمجيات تلبي احتياجاتكم الخاصة وتتناسب مع متطلبات أعمالكم</p>
                </div>
                <div class="feature-card fade-in-element delay-3">
                    <h3>تقنيات حديثة</h3>
                    <p>استخدام أحدث التقنيات والأدوات في مجال تطوير البرمجيات</p>
                </div>
                <div class="feature-card fade-in-element delay-4">
                    <h3>جودة عالية</h3>
                    <p>ضمان تقديم برمجيات عالية الجودة ومختبرة بشكل شامل</p>
                </div>
                <div class="feature-card fade-in-element delay-5">
                    <h3>دعم مستمر</h3>
                    <p>توفير الدعم الفني والصيانة المستمرة لجميع البرمجيات</p>
                </div>
                <div class="feature-card fade-in-element delay-6">
                    <h3>تكلفة منافسة</h3>
                    <p>أسعار تنافسية مع ضمان أفضل قيمة مقابل المال</p>
                </div>
            </div>
        </section>

        <!-- Call to Action -->
        <section class="contact-cta fade-in-element delay-5">
            <h2 style="color: #008B80; margin-bottom: 20px;">هل تحتاج إلى حلول برمجية متطورة؟</h2>
            <p style="margin-bottom: 30px; color: #c0c0c0;">تواصل معنا اليوم للحصول على استشارة مجانية وتقييم احتياجاتكم البرمجية</p>
            <a href="index.html#contact" class="cta-button">تواصل معنا</a>
            <a href="tel:+966123456789" class="cta-button">اتصل بنا</a>
        </section>
    </main>
    <script>
        function createParticles() {
            const particlesContainer = document.getElementById('particles');
            const particleCount = 30;
            for (let i = 0; i < particleCount; i++) {
                const particle = document.createElement('div');
                particle.className = 'particle';
                particle.style.left = Math.random() * 100 + '%';
                particle.style.top = Math.random() * 100 + '%';
                particle.style.animationDelay = Math.random() * 8 + 's';
                particle.style.animationDuration = (Math.random() * 4 + 4) + 's';
                particlesContainer.appendChild(particle);
            }
        }
        // تأثير التبديل السلس للغة
        function switchLanguage(lang) {
            if (lang === 'en') {
                sessionStorage.setItem('pageTransition', 'true');
                window.location.href = 'software-en.html';
            }
        }

        // إدارة تحميل الصفحة
        function handlePageLoad() {
            const pageLoader = document.getElementById('pageLoader');
            const isTransition = sessionStorage.getItem('pageTransition');

            if (isTransition) {
                sessionStorage.removeItem('pageTransition');
                setTimeout(() => pageLoader.classList.add('fade-out'), 2000);
                setTimeout(() => pageLoader.style.display = 'none', 2800);
            } else {
                pageLoader.style.display = 'none';
            }
        }

        window.addEventListener('load', () => {
            handlePageLoad();
            createParticles();
        });

        // إعداد الشكل المتحرك في صفحة البرمجيات
        document.addEventListener('DOMContentLoaded', function() {
            const dashboardContainer = document.querySelector('.dashboard-container');
            const imageOrbit = document.querySelector('.image-orbit');

            if (!dashboardContainer || !imageOrbit) return;

            let currentRotation = 0;
            let isRotating = false;
            let animationFrame;
            let lastTimestamp = 0;
            const rotationSpeed = 0.5;

            function animate(timestamp) {
                if (!lastTimestamp) lastTimestamp = timestamp;

                if (isRotating) {
                    currentRotation += rotationSpeed;
                    if (currentRotation >= 360) {
                        currentRotation = currentRotation % 360;
                    }
                    imageOrbit.style.transform = `rotate(${currentRotation}deg)`;
                    lastTimestamp = timestamp;
                    animationFrame = requestAnimationFrame(animate);
                }
            }

            dashboardContainer.addEventListener('mouseenter', function() {
                if (!isRotating) {
                    isRotating = true;
                    lastTimestamp = 0;
                    animationFrame = requestAnimationFrame(animate);
                }
            });

            dashboardContainer.addEventListener('mouseleave', function() {
                isRotating = false;
                cancelAnimationFrame(animationFrame);
                imageOrbit.style.transition = 'none';
            });
        });

    </script>
    <script src="js/dashboard-animation.js"></script>
    <script src="js/scroll-animations.js"></script>
    <script src="js/mobile-nav.js"></script>
    <script src="js/device-detection.js"></script>
</body>
</html>
