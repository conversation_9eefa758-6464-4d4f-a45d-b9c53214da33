/* ===================================
   ANIMATION STABILITY - PREVENT SPEED CHANGES
   ===================================*/

(function() {
    'use strict';
    
    // Store original animation properties
    const originalAnimations = new Map();
    let isInitialized = false;
    
    // Initialize when DOM is ready
    function init() {
        if (isInitialized) return;
        
        console.log('Animation Stability: Initializing...');
        
        // Wait a bit for all CSS to load
        setTimeout(() => {
            captureOriginalAnimations();
            setupProtection();
            isInitialized = true;
            console.log('Animation Stability: Protection active');
        }, 1000);
    }
    
    function captureOriginalAnimations() {
        // Capture hero logo
        const heroLogo = document.querySelector('.hero-logo');
        if (heroLogo) {
            captureElementAnimation(heroLogo, 'hero-logo');
        }
        
        // Capture floating images
        const floatingImages = document.querySelectorAll('.floating-image-1, .floating-image-2, .decorative-image');
        floatingImages.forEach((element, index) => {
            captureElementAnimation(element, `floating-${index}`);
        });
        
        // Capture meteors
        const meteors = document.querySelectorAll('.meteor');
        meteors.forEach((element, index) => {
            captureElementAnimation(element, `meteor-${index}`);
        });
    }
    
    function captureElementAnimation(element, id) {
        const computedStyle = window.getComputedStyle(element);
        
        originalAnimations.set(id, {
            element: element,
            animationName: computedStyle.animationName,
            animationDuration: computedStyle.animationDuration,
            animationTimingFunction: computedStyle.animationTimingFunction,
            animationIterationCount: computedStyle.animationIterationCount,
            animationDirection: computedStyle.animationDirection,
            animationFillMode: computedStyle.animationFillMode,
            animationDelay: computedStyle.animationDelay
        });
        
        console.log(`Captured animation for ${id}:`, computedStyle.animationDuration);
    }
    
    function restoreOriginalAnimations() {
        originalAnimations.forEach((data, id) => {
            const element = data.element;
            if (element && element.style) {
                // Force restore original properties
                element.style.animationName = data.animationName;
                element.style.animationDuration = data.animationDuration;
                element.style.animationTimingFunction = data.animationTimingFunction;
                element.style.animationIterationCount = data.animationIterationCount;
                element.style.animationDirection = data.animationDirection;
                element.style.animationFillMode = data.animationFillMode;
                element.style.animationDelay = data.animationDelay;
                element.style.animationPlayState = 'running';
            }
        });
    }
    
    function setupProtection() {
        // Method 1: Page Visibility API
        document.addEventListener('visibilitychange', function() {
            if (!document.hidden) {
                // Page became visible - restore animations
                setTimeout(restoreOriginalAnimations, 100);
            }
        });
        
        // Method 2: Window focus
        window.addEventListener('focus', function() {
            setTimeout(restoreOriginalAnimations, 100);
        });
        
        // Method 3: Page show event
        window.addEventListener('pageshow', function() {
            setTimeout(restoreOriginalAnimations, 100);
        });
        
        // Method 4: Periodic check every 3 seconds
        setInterval(function() {
            if (!document.hidden && document.hasFocus()) {
                restoreOriginalAnimations();
            }
        }, 3000);
        
        // Method 5: Mouse movement detection (user is active)
        let mouseTimer;
        document.addEventListener('mousemove', function() {
            clearTimeout(mouseTimer);
            mouseTimer = setTimeout(restoreOriginalAnimations, 500);
        });
    }
    
    // Initialize based on document state
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', init);
    } else {
        init();
    }
    
    // Expose for manual control
    window.animationStability = {
        restore: restoreOriginalAnimations,
        capture: captureOriginalAnimations,
        data: originalAnimations
    };
    
})();

/* ===================================
   CSS ANIMATION PROTECTION
   ===================================*/

// Add CSS protection immediately
(function() {
    const protectionCSS = document.createElement('style');
    protectionCSS.id = 'animation-protection';
    protectionCSS.textContent = `
        /* Force stable animation durations */
        .hero-logo {
            animation-name: heroFloat !important;
            animation-duration: 8s !important;
            animation-timing-function: ease-in-out !important;
            animation-iteration-count: infinite !important;
            animation-direction: alternate !important;
            animation-fill-mode: both !important;
        }
        
        .floating-image-1, .decorative-image:nth-child(odd) {
            animation-name: edgeOrbitCW !important;
            animation-duration: 60s !important;
            animation-timing-function: linear !important;
            animation-iteration-count: infinite !important;
            animation-fill-mode: both !important;
        }
        
        .floating-image-2, .decorative-image:nth-child(even) {
            animation-name: edgeOrbitCCW !important;
            animation-duration: 60s !important;
            animation-timing-function: linear !important;
            animation-iteration-count: infinite !important;
            animation-fill-mode: both !important;
        }
        
        /* Prevent any external modifications */
        .hero-logo,
        .floating-image-1,
        .floating-image-2,
        .decorative-image {
            animation-play-state: running !important;
        }
        
        /* Override any mobile performance optimizations */
        @media (max-width: 767px) {
            .hero-logo {
                animation-duration: 8s !important;
            }
            
            .floating-image-1,
            .floating-image-2,
            .decorative-image {
                animation-duration: 60s !important;
            }
        }
    `;
    
    // Add to head immediately
    if (document.head) {
        document.head.appendChild(protectionCSS);
    } else {
        document.addEventListener('DOMContentLoaded', function() {
            document.head.appendChild(protectionCSS);
        });
    }
})();
