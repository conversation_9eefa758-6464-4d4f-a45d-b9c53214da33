// <PERSON>ript to update all service pages with mobile navigation
// This is a helper script to identify what needs to be updated

const servicePages = [
    'networking-en.html',
    'maintenance.html', 
    'maintenance-en.html',
    'software.html',
    'software-en.html',
    'cybersecurity.html',
    'cybersecurity-en.html'
];

console.log('Service pages that need updating:', servicePages);

// CSS files to add to each page:
const cssFiles = [
    'css/responsive.css',
    'css/device-adaptive.css', 
    'css/service-pages-navbar.css',
    'css/mobile-fixes.css'
];

// JS files to add to each page:
const jsFiles = [
    'js/mobile-nav.js',
    'js/device-detection.js'
];

console.log('CSS files to add:', cssFiles);
console.log('JS files to add:', jsFiles);
