<!DOCTYPE html>
<html lang="en" dir="ltr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Support & Maintenance - MCT</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="css/style.css">
    <link rel="stylesheet" href="css/responsive.css">
    <link rel="stylesheet" href="css/device-adaptive.css">
    <link rel="stylesheet" href="css/service-pages-navbar.css">
    <link rel="stylesheet" href="css/mobile-fixes.css">
    <style>
        /* Font Application for Maintenance Page */
        * {
            font-family: 'NotoNaskhArabic', 'Arial', sans-serif;
        }
        
        body {
            font-family: 'NotoNaskhArabic', 'Arial', sans-serif;
        }
        
        h1, h2, h3, h4, h5, h6 {
            font-family: 'NotoNaskhArabic', 'Arial', sans-serif;
            font-weight: 700;
        }
        
        .cta-button, button {
            font-family: 'NotoNaskhArabic', 'Arial', sans-serif;
            font-weight: 600;
        }
        
        .nav-links a {
            font-family: 'NotoNaskhArabic', 'Arial', sans-serif;
            font-weight: 500;
        }
    </style>
</head>
<body class="service-page">
    <!-- شاشة التحميل -->
    <div class="page-loader" id="pageLoader">
        <div class="loader-logo">
            <img src="image/logo-8.png" alt="MCT Logo">
            <div class="loader-text">Support & Maintenance</div>
        </div>
    </div>

    <div class="animated-bg"></div>
    <div class="floating-particles" id="particles"></div>
    <nav class="navbar">
        <div class="nav-container">
            <div class="logo-section">
                <a href="index-en.html" class="logo">
                    <img src="image/logo-11.png" alt="MCT Logo">
                </a>
                <a href="index-en.html" class="logo">
                    <img src="image/logo-10.png" alt="MCT Logo">
                </a>
            </div>
            <div class="nav-center">
                <button class="hamburger" id="hamburger-menu" aria-label="Open menu">
                    <span></span>
                    <span></span>
                    <span></span>
                </button>
                <ul class="nav-links" id="nav-links">
                    <li><a href="networking-en.html">Network Solutions</a></li>
                    <li><a href="software-en.html">Software Development</a></li>
                    <li><a href="cybersecurity-en.html">Information Security</a></li>
                    <li><a href="maintenance-en.html" class="active">Support & Maintenance</a></li>
                    <li><a href="index-en.html">Back to Home</a></li>
                    <li><button id="lang-toggle" onclick="switchLanguage('ar')">AR</button></li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- المحتوى الرئيسي -->
    <main class="main-content">
        <!-- Service Hero -->
        <section class="service-hero fade-in-element">
            <div class="service-icon"><i class="fas fa-tools"></i></div>
            <h1 class="service-title fade-in-element delay-1">Network Maintenance</h1>
            <p class="service-subtitle fade-in-element delay-2">
                We provide comprehensive network maintenance services to ensure continuous operation and efficiency. Our team of experts ensures regular and preventive maintenance for your network.
            </p>
        </section>

        <!-- Our Services -->
        <section class="content-section fade-in-element delay-3">
            <h2 class="section-title fade-in-element">Our Maintenance Services</h2>
            <ul class="services-list fade-in-element delay-1">
                <li><strong>Preventive Maintenance:</strong> Regular network inspection and software/hardware updates</li>
                <li><strong>Fault Repair:</strong> Quick and effective handling of any network issues</li>
                <li><strong>System Updates:</strong> Installation and updating of network software and hardware</li>
                <li><strong>Performance Monitoring:</strong> Continuous monitoring of network performance and early problem detection</li>
                <li><strong>Backup:</strong> Protection of network data through regular backups</li>
                <li><strong>Staff Training:</strong> Training the work team on network handling</li>
                <li><strong>Performance Reports:</strong> Regular reports on network status and performance</li>
            </ul>
        </section>

        <section class="content-section fade-in-element delay-4">
            <h2 class="section-title fade-in-element">Why Choose Our Services?</h2>
            <div class="features-grid">
                <div class="feature-card fade-in-element delay-1">
                    <h3>Extensive Experience</h3>
                    <p>A team of experts specialized in network maintenance</p>
                </div>
                <div class="feature-card fade-in-element delay-2">
                    <h3>Immediate Support</h3>
                    <p>Quick response to maintenance requests and technical support</p>
                </div>
                <div class="feature-card fade-in-element delay-3">
                    <h3>Integrated Solutions</h3>
                    <p>Comprehensive maintenance services covering all aspects of the network</p>
                </div>
                <div class="feature-card fade-in-element delay-4">
                    <h3>Advanced Technologies</h3>
                    <p>Use of the latest technologies and tools in maintenance</p>
                </div>
                <div class="feature-card fade-in-element delay-5">
                    <h3>Competitive Cost</h3>
                    <p>Effective maintenance services at competitive prices suitable for various budgets</p>
                </div>
                <div class="feature-card fade-in-element delay-6">
                    <h3>Quality Assurance</h3>
                    <p>Commitment to global quality standards in maintenance services</p>
                </div>
            </div>
        </section>

        <!-- Call to Action -->
        <section class="contact-cta fade-in-element delay-5">
            <h2 style="color: #008B80; margin-bottom: 20px;">Do You Need Network Maintenance?</h2>
            <p style="margin-bottom: 30px; color: #c0c0c0;">Contact us today for a free consultation and comprehensive assessment of your needs</p>
            <a href="index-en.html#contact" class="cta-button">Contact Us</a>
            <a href="tel:+966123456789" class="cta-button">Call Us</a>
        </section>
    </main>
    <script>
        function createParticles() {
            const particlesContainer = document.getElementById('particles');
            const particleCount = 30;
            for (let i = 0; i < particleCount; i++) {
                const particle = document.createElement('div');
                particle.className = 'particle';
                particle.style.left = Math.random() * 100 + '%';
                particle.style.top = Math.random() * 100 + '%';
                particle.style.animationDelay = Math.random() * 8 + 's';
                particle.style.animationDuration = (Math.random() * 4 + 4) + 's';
                particlesContainer.appendChild(particle);
            }
        }
        // تأثير التبديل السلس للغة
        function switchLanguage(lang) {
            if (lang === 'ar') {
                sessionStorage.setItem('pageTransition', 'true');
                window.location.href = 'maintenance.html';
            }
        }

        // إدارة تحميل الصفحة
        function handlePageLoad() {
            const pageLoader = document.getElementById('pageLoader');
            const isTransition = sessionStorage.getItem('pageTransition');

            if (isTransition) {
                sessionStorage.removeItem('pageTransition');
                setTimeout(() => pageLoader.classList.add('fade-out'), 2000);
                setTimeout(() => pageLoader.style.display = 'none', 2800);
            } else {
                pageLoader.style.display = 'none';
            }
        }

        window.addEventListener('load', () => {
            handlePageLoad();
            createParticles();
        });

    </script>
    <script src="js/dashboard-animation.js"></script>
    <script src="js/scroll-animations.js"></script>
    <script src="js/mobile-nav.js"></script>
    <script src="js/device-detection.js"></script>
</body>
</html>
