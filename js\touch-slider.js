/* ===================================
   TOUCH GESTURES FOR SLIDER
   ===================================*/

document.addEventListener('DOMContentLoaded', function() {
    const slider = document.getElementById('slider');
    const slidesWrapper = document.getElementById('slidesWrapper');
    const slides = document.querySelectorAll('.slide');
    const dots = document.querySelectorAll('.dot');
    
    if (!slider || !slidesWrapper || !slides.length) return;
    
    let currentIndex = 0;
    let isAnimating = false;
    let startX = 0;
    let currentX = 0;
    let isDragging = false;
    let startTime = 0;
    
    // Touch event handlers
    function handleTouchStart(e) {
        if (isAnimating) return;
        
        startX = e.touches[0].clientX;
        currentX = startX;
        startTime = Date.now();
        isDragging = true;
        
        slidesWrapper.style.transition = 'none';
        slider.style.cursor = 'grabbing';
    }
    
    function handleTouchMove(e) {
        if (!isDragging || isAnimating) return;
        
        e.preventDefault();
        currentX = e.touches[0].clientX;
        const deltaX = currentX - startX;
        const currentTransform = currentIndex * (slides[0].offsetWidth + 20);
        
        // Apply transform with resistance at boundaries
        let newTransform = currentTransform - deltaX;
        
        // Add resistance at boundaries
        if (currentIndex === 0 && deltaX > 0) {
            newTransform = currentTransform - (deltaX * 0.3);
        } else if (currentIndex === slides.length - 1 && deltaX < 0) {
            newTransform = currentTransform - (deltaX * 0.3);
        }
        
        slidesWrapper.style.transform = `translateX(${newTransform}px)`;
    }
    
    function handleTouchEnd(e) {
        if (!isDragging) return;
        
        isDragging = false;
        slider.style.cursor = 'grab';
        
        const deltaX = currentX - startX;
        const deltaTime = Date.now() - startTime;
        const velocity = Math.abs(deltaX) / deltaTime;
        
        // Determine if we should change slides
        const threshold = slides[0].offsetWidth * 0.25; // 25% of slide width
        const fastSwipe = velocity > 0.5; // Fast swipe detection
        
        let newIndex = currentIndex;
        
        if (Math.abs(deltaX) > threshold || fastSwipe) {
            if (deltaX > 0 && currentIndex > 0) {
                newIndex = currentIndex - 1;
            } else if (deltaX < 0 && currentIndex < slides.length - 1) {
                newIndex = currentIndex + 1;
            }
        }
        
        goToSlide(newIndex);
    }
    
    // Mouse event handlers for desktop
    function handleMouseDown(e) {
        if (window.innerWidth > 768) return; // Only on mobile
        
        e.preventDefault();
        startX = e.clientX;
        currentX = startX;
        startTime = Date.now();
        isDragging = true;
        
        slidesWrapper.style.transition = 'none';
        slider.style.cursor = 'grabbing';
        
        document.addEventListener('mousemove', handleMouseMove);
        document.addEventListener('mouseup', handleMouseUp);
    }
    
    function handleMouseMove(e) {
        if (!isDragging) return;
        
        e.preventDefault();
        currentX = e.clientX;
        const deltaX = currentX - startX;
        const currentTransform = currentIndex * (slides[0].offsetWidth + 20);
        
        let newTransform = currentTransform - deltaX;
        
        if (currentIndex === 0 && deltaX > 0) {
            newTransform = currentTransform - (deltaX * 0.3);
        } else if (currentIndex === slides.length - 1 && deltaX < 0) {
            newTransform = currentTransform - (deltaX * 0.3);
        }
        
        slidesWrapper.style.transform = `translateX(${newTransform}px)`;
    }
    
    function handleMouseUp(e) {
        if (!isDragging) return;
        
        isDragging = false;
        slider.style.cursor = 'grab';
        
        const deltaX = currentX - startX;
        const deltaTime = Date.now() - startTime;
        const velocity = Math.abs(deltaX) / deltaTime;
        
        const threshold = slides[0].offsetWidth * 0.25;
        const fastSwipe = velocity > 0.5;
        
        let newIndex = currentIndex;
        
        if (Math.abs(deltaX) > threshold || fastSwipe) {
            if (deltaX > 0 && currentIndex > 0) {
                newIndex = currentIndex - 1;
            } else if (deltaX < 0 && currentIndex < slides.length - 1) {
                newIndex = currentIndex + 1;
            }
        }
        
        goToSlide(newIndex);
        
        document.removeEventListener('mousemove', handleMouseMove);
        document.removeEventListener('mouseup', handleMouseUp);
    }
    
    // Go to specific slide
    function goToSlide(index) {
        if (isAnimating || index === currentIndex) return;
        
        isAnimating = true;
        currentIndex = index;
        
        slidesWrapper.style.transition = 'transform 0.5s cubic-bezier(0.25, 0.46, 0.45, 0.94)';
        slidesWrapper.style.transform = `translateX(${currentIndex * (slides[0].offsetWidth + 20)}px)`;
        
        // Update dots
        dots.forEach((dot, i) => {
            dot.classList.toggle('active', i === currentIndex);
        });
        
        // Update active slide
        slides.forEach((slide, i) => {
            slide.classList.toggle('active', i === currentIndex);
        });
        
        setTimeout(() => {
            isAnimating = false;
        }, 500);
    }
    
    // Add event listeners
    slider.addEventListener('touchstart', handleTouchStart, { passive: false });
    slider.addEventListener('touchmove', handleTouchMove, { passive: false });
    slider.addEventListener('touchend', handleTouchEnd, { passive: true });
    
    slider.addEventListener('mousedown', handleMouseDown);
    
    // Prevent context menu on long press
    slider.addEventListener('contextmenu', function(e) {
        e.preventDefault();
    });
    
    // Handle window resize
    window.addEventListener('resize', function() {
        if (!isAnimating) {
            slidesWrapper.style.transition = 'none';
            slidesWrapper.style.transform = `translateX(${currentIndex * (slides[0].offsetWidth + 20)}px)`;
            
            setTimeout(() => {
                slidesWrapper.style.transition = 'transform 0.5s cubic-bezier(0.25, 0.46, 0.45, 0.94)';
            }, 100);
        }
    });
    
    // Keyboard navigation
    document.addEventListener('keydown', function(e) {
        if (e.key === 'ArrowLeft' && currentIndex > 0) {
            goToSlide(currentIndex - 1);
        } else if (e.key === 'ArrowRight' && currentIndex < slides.length - 1) {
            goToSlide(currentIndex + 1);
        }
    });
    
    // Dot navigation with touch support
    dots.forEach((dot, index) => {
        dot.addEventListener('click', () => goToSlide(index));
        dot.addEventListener('touchend', (e) => {
            e.preventDefault();
            goToSlide(index);
        });
    });
    
    // Arrow navigation with touch support
    const prevArrow = document.getElementById('prevArrow');
    const nextArrow = document.getElementById('nextArrow');
    
    if (prevArrow) {
        prevArrow.addEventListener('click', () => {
            if (currentIndex > 0) goToSlide(currentIndex - 1);
        });
        prevArrow.addEventListener('touchend', (e) => {
            e.preventDefault();
            if (currentIndex > 0) goToSlide(currentIndex - 1);
        });
    }
    
    if (nextArrow) {
        nextArrow.addEventListener('click', () => {
            if (currentIndex < slides.length - 1) goToSlide(currentIndex + 1);
        });
        nextArrow.addEventListener('touchend', (e) => {
            e.preventDefault();
            if (currentIndex < slides.length - 1) goToSlide(currentIndex + 1);
        });
    }
    
    // Initialize
    goToSlide(0);
});

/* ===================================
   MOBILE PERFORMANCE OPTIMIZATIONS
   ===================================*/

// Optimize for mobile performance
if ('ontouchstart' in window) {
    document.addEventListener('DOMContentLoaded', function() {
        // Add will-change property for better performance
        const slidesWrapper = document.getElementById('slidesWrapper');
        if (slidesWrapper) {
            slidesWrapper.style.willChange = 'transform';
        }
        
        // Optimize images for mobile
        const slideImages = document.querySelectorAll('.slide img');
        slideImages.forEach(img => {
            img.style.willChange = 'transform';
            img.loading = 'lazy';
        });
    });
}
