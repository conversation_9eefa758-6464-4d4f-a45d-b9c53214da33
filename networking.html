<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>الشبكات - MCT</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="css/style.css">
    <link rel="stylesheet" href="css/responsive.css">
    <link rel="stylesheet" href="css/device-adaptive.css">
    <link rel="stylesheet" href="css/service-pages-navbar.css">
    <link rel="stylesheet" href="css/mobile-fixes.css">
</head>
<body class="service-page">
    <!-- شاشة التحميل -->
    <div class="page-loader" id="pageLoader">
        <div class="loader-logo">
            <img src="image/logo-8.png" alt="MCT Logo">
            <div class="loader-text">Network Solutions</div>
        </div>
    </div>


    <div class="animated-bg"></div>
    <div class="floating-particles" id="particles"></div>

    <!-- Header -->
    <nav class="navbar">
        <div class="nav-container">
            <div class="logo-section">
                <a href="index.html" class="logo">
                    <img src="image/logo-11.png" alt="MCT Logo">
                </a>
                <a href="index.html" class="logo">
                    <img src="image/logo-10.png" alt="MCT Logo">
                </a>
            </div>
            <div class="nav-center">
                <button class="hamburger" id="hamburger-menu" aria-label="Open menu">
                    <span></span>
                    <span></span>
                    <span></span>
                </button>
                <ul class="nav-links" id="nav-links">
                    <li><a href="networking.html" class="active">حلول الشبكات</a></li>
                    <li><a href="software.html">تطوير البرمجيات</a></li>
                    <li><a href="cybersecurity.html">أمن المعلومات</a></li>
                    <li><a href="maintenance.html">الدعم والصيانة</a></li>
                    <li><a href="index.html">العودة للرئيسية</a></li>
                    <li><button id="lang-toggle" onclick="switchLanguage('en')">EN</button></li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <main class="main-content">
        <!-- Hero Section -->
        <section class="service-hero fade-in-element">
            <div class="service-icon"><i class="fas fa-network-wired"></i></div>
            <h1 class="service-title fade-in-element delay-1">حلول الشبكات</h1>
            <p class="service-subtitle fade-in-element delay-2">
                نقدم حلول شبكات متكاملة وموثوقة تضمن الاتصال السلس والآمن لجميع الأنظمة,
                من التصميم والتنفيذ إلى الصيانة والدعم.
            </p>
        </section>

        <!-- خدماتنا -->
        <section class="content-section fade-in-element delay-3">
            <h2 class="section-title fade-in-element">خدماتنا في الشبكات</h2>
            <div class="services-with-animation">
                <div class="services-text">
                    <ul class="services-list fade-in-element delay-1">
                        <li><strong>تحليل احتياجات الشبكة:</strong> تصميم شبكات مخصصة تتناسب مع احتياج وحجم أعمالكم</li>
                        <li><strong>تنفيذ وتركيب:</strong> تركيب الأجهزة والبرمجيات اللازمة.</li>
                        <li><strong>غرفة البيانات</strong></li>
                        <li><strong>البيئات الافتراضية</strong></li>
                        <li><strong>الصيانة والدعم: </strong> تقديم خدمات الدعم الفني لضمان استمرارية العمل.</li>
                        <li><strong>الشبكات اللاسلكية الداخلية.</strong></li>
                    </ul>
                </div>
                <div class="services-animation">
                    <div class="network-globe-container">
                        <div class="network-container" id="networkContainer">
                            
                            <div class="floating-particles" id="particles"></div>
                            
                            <svg class="connection-svg">
                                <path class="curved-path" d="M 300 160 Q 380 190 440 300" />
                                <path class="curved-path" d="M 440 300 Q 380 410 300 440" />
                                <path class="curved-path" d="M 300 440 Q 220 410 160 300" />
                                <path class="curved-path" d="M 160 300 Q 220 190 300 160" />
                            </svg>
                            
                            <div class="globe"></div>
                            
                            <div class="user-avatar user1">
                                <div class="avatar-head"></div>
                                <div class="avatar-body"></div>
                            </div>
                            
                            <div class="user-avatar user2">
                                <div class="avatar-head"></div>
                                <div class="avatar-body"></div>
                            </div>
                            
                            <div class="user-avatar user3">
                                <div class="avatar-head"></div>
                                <div class="avatar-body"></div>
                            </div>
                            
                            <div class="user-avatar user4">
                                <div class="avatar-head"></div>
                                <div class="avatar-body"></div>
                            </div>
                            
                            <div class="moving-dot dot1"></div>
                            <div class="moving-dot dot2"></div>
                            <div class="moving-dot dot3"></div>
                            <div class="moving-dot dot4"></div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <section class="content-section fade-in-element delay-4">
            <h2 class="section-title fade-in-element">لماذا تختار خدماتنا؟</h2>
            <div class="features-grid">
                <div class="feature-card fade-in-element delay-1">
                    <h3>أداء عالي</h3>
                    <p>شبكات عالية السرعة والكفاءة تضمن أداءً مثالياً لجميع تطبيقاتكم وأنظمتكم</p>
                </div>
                <div class="feature-card fade-in-element delay-2">
                    <h3>موثوقية تامة</h3>
                    <p>حلول شبكات موثوقة مع ضمانات الجودة وخطط النسخ الاحتياطي للحماية من الأعطال</p>
                </div>
                <div class="feature-card fade-in-element delay-3">
                    <h3>أمان متقدم</h3>
                    <p>تطبيق أعلى معايير الأمان لحماية شبكتكم من التهديدات والوصول غير المصرح</p>
                </div>
                <div class="feature-card fade-in-element delay-4">
                    <h3>قابلية التوسع</h3>
                    <p>شبكات مرنة قابلة للتوسع والتطوير مع نمو أعمالكم ومتطلباتكم المستقبلية</p>
                </div>
                <div class="feature-card fade-in-element delay-5">
                    <h3>دعم فني متميز</h3>
                    <p>فريق دعم فني متخصص متاح على مدار الساعة لحل أي مشاكل أو استفسارات</p>
                </div>
                <div class="feature-card fade-in-element delay-6">
                    <h3>تكلفة مثلى</h3>
                    <p>حلول فعالة من حيث التكلفة تحقق أفضل عائد على الاستثمار لمشاريعكم</p>
                </div>
            </div>
        </section>

        <!-- Call to Action -->
        <section class="contact-cta fade-in-element delay-5">
            <h2 style="color: #008B80; margin-bottom: 20px;">هل تحتاج لشبكة موثوقة وعالية الأداء؟</h2>
            <p style="margin-bottom: 30px; color: #c0c0c0;">تواصل معنا اليوم للحصول على استشارة مجانية وتقييم شامل لاحتياجاتكم من الشبكات</p>
            <a href="index.html#contact" class="cta-button">تواصل معنا</a>
            <a href="tel:+966123456789" class="cta-button">اتصل بنا</a>
        </section>
    </main>

    <script>
        // إنشاء الجسيمات المتحركة
        function createParticles() {
            const particlesContainer = document.getElementById('particles');
            const particleCount = 30;

            for (let i = 0; i < particleCount; i++) {
                const particle = document.createElement('div');
                particle.className = 'particle';
                particle.style.left = Math.random() * 100 + '%';
                particle.style.top = Math.random() * 100 + '%';
                particle.style.animationDelay = Math.random() * 8 + 's';
                particle.style.animationDuration = (Math.random() * 4 + 4) + 's';
                particlesContainer.appendChild(particle);
            }
        }

        // Language Switch Function
        function switchLanguage(lang) {
            if (lang === 'en') {
                sessionStorage.setItem('pageTransition', 'true');
                window.location.href = 'networking-en.html';
            }
        }

        // إدارة تحميل الصفحة
        function handlePageLoad() {
            const pageLoader = document.getElementById('pageLoader');
            const isTransition = sessionStorage.getItem('pageTransition');

            if (isTransition) {
                sessionStorage.removeItem('pageTransition');
                setTimeout(() => pageLoader.classList.add('fade-out'), 2000);
                setTimeout(() => pageLoader.style.display = 'none', 2800);
            } else {
                pageLoader.style.display = 'none';
            }
        }

        // تهيئة الصفحة
        window.addEventListener('load', () => {
            handlePageLoad();
            createParticles();
            setupNetworkGlobe();
        });

        // إعداد الشكل التفاعلي للشبكة العالمية
        function setupNetworkGlobe() {
            const networkContainer = document.getElementById('networkContainer');
            const particlesContainer = document.getElementById('particles');
            const globe = document.querySelector('.globe');
            
            if (!networkContainer || !particlesContainer || !globe) {
                console.log('Network globe elements not found');
                return;
            }

            console.log('Setting up network globe...');

            // إنشاء الجسيمات المتحركة
            function createGlobeParticles() {
                for (let i = 0; i < 15; i++) {
                    const particle = document.createElement('div');
                    particle.className = 'particle';
                    particle.style.left = Math.random() * 100 + '%';
                    particle.style.top = Math.random() * 100 + '%';
                    particle.style.animationDelay = Math.random() * 6 + 's';
                    particle.style.animationDuration = (Math.random() * 4 + 4) + 's';
                    particlesContainer.appendChild(particle);
                }
            }

            // دوران الكرة الأرضية
            let rotationAngle = 0;
            function rotateGlobe() {
                rotationAngle += 0.5;
                globe.style.transform = `translate(-50%, -50%) rotate(${rotationAngle}deg)`;
                requestAnimationFrame(rotateGlobe);
            }

            // تأثير النقر
            networkContainer.addEventListener('click', (e) => {
                const ripple = document.createElement('div');
                ripple.style.position = 'absolute';
                ripple.style.width = '20px';
                ripple.style.height = '20px';
                ripple.style.background = 'rgba(0, 198, 255, 0.5)';
                ripple.style.borderRadius = '50%';
                ripple.style.left = (e.clientX - networkContainer.getBoundingClientRect().left - 10) + 'px';
                ripple.style.top = (e.clientY - networkContainer.getBoundingClientRect().top - 10) + 'px';
                ripple.style.animation = 'ripple 0.6s ease-out forwards';
                ripple.style.pointerEvents = 'none';
                ripple.style.zIndex = '10';
                
                networkContainer.appendChild(ripple);
                
                setTimeout(() => {
                    ripple.remove();
                }, 600);
            });

            // بدء الحركة
            createGlobeParticles();
            rotateGlobe();
        }

    </script>
    <script src="js/dashboard-animation.js"></script>
    <script src="js/scroll-animations.js"></script>
    <script src="js/mobile-nav.js"></script>
    <script src="js/device-detection.js"></script>
</body>
</html>
