/* ===================================
   STABLE ANIMATIONS - FINAL SOLUTION
   Uses JavaScript instead of CSS animations to prevent speed issues
   ===================================*/

(function() {
    'use strict';
    
    let isPageVisible = true;
    let animationFrame;
    let startTime = Date.now();
    
    // Animation objects
    const animations = {
        heroLogo: {
            element: null,
            duration: 8000, // 8 seconds
            startY: 0,
            endY: -10,
            type: 'float'
        },
        floatingImage1: {
            element: null,
            duration: 60000, // 60 seconds
            type: 'orbit',
            direction: 'cw'
        },
        floatingImage2: {
            element: null,
            duration: 60000, // 60 seconds
            type: 'orbit',
            direction: 'ccw'
        }
    };
    
    // Initialize when DOM is ready
    function init() {
        console.log('Stable Animations: Initializing...');
        
        // Find elements
        animations.heroLogo.element = document.querySelector('.hero-logo');
        animations.floatingImage1.element = document.querySelector('.floating-image-1');
        animations.floatingImage2.element = document.querySelector('.floating-image-2');
        
        // Remove CSS animations
        removeCSSAnimations();
        
        // Setup visibility listeners
        setupVisibilityListeners();
        
        // Start JavaScript animations
        startAnimations();
        
        console.log('Stable Animations: Started');
    }
    
    function removeCSSAnimations() {
        // Remove CSS animations to prevent conflicts
        const style = document.createElement('style');
        style.textContent = `
            .hero-logo {
                animation: none !important;
            }
            .floating-image-1,
            .floating-image-2,
            .decorative-image {
                animation: none !important;
            }
        `;
        document.head.appendChild(style);
    }
    
    function setupVisibilityListeners() {
        document.addEventListener('visibilitychange', function() {
            if (document.hidden) {
                isPageVisible = false;
                stopAnimations();
                console.log('Page hidden - animations stopped');
            } else {
                isPageVisible = true;
                startTime = Date.now(); // Reset start time
                startAnimations();
                console.log('Page visible - animations restarted');
            }
        });
        
        window.addEventListener('blur', function() {
            isPageVisible = false;
            stopAnimations();
        });
        
        window.addEventListener('focus', function() {
            isPageVisible = true;
            startTime = Date.now(); // Reset start time
            startAnimations();
        });
    }
    
    function startAnimations() {
        if (animationFrame) {
            cancelAnimationFrame(animationFrame);
        }
        
        function animate() {
            if (!isPageVisible) return;
            
            const currentTime = Date.now();
            const elapsed = currentTime - startTime;
            
            // Animate hero logo
            animateHeroLogo(elapsed);
            
            // Animate floating images
            animateFloatingImage1(elapsed);
            animateFloatingImage2(elapsed);
            
            animationFrame = requestAnimationFrame(animate);
        }
        
        animate();
    }
    
    function stopAnimations() {
        if (animationFrame) {
            cancelAnimationFrame(animationFrame);
            animationFrame = null;
        }
    }
    
    function animateHeroLogo(elapsed) {
        const logo = animations.heroLogo.element;
        if (!logo) return;
        
        const duration = animations.heroLogo.duration;
        const progress = (elapsed % duration) / duration;
        
        // Create smooth up-down motion
        const y = Math.sin(progress * Math.PI * 2) * -10;
        
        logo.style.transform = `translateY(${y}px)`;
    }
    
    function animateFloatingImage1(elapsed) {
        const img = animations.floatingImage1.element;
        if (!img) return;
        
        const container = img.parentElement;
        if (!container) return;
        
        const duration = animations.floatingImage1.duration;
        const progress = (elapsed % duration) / duration;
        
        // Calculate orbit position (clockwise)
        const containerRect = container.getBoundingClientRect();
        const containerWidth = containerRect.width;
        const containerHeight = containerRect.height;
        
        let x, y;
        
        if (progress < 0.25) {
            // Top edge, left to right
            const edgeProgress = progress / 0.25;
            x = edgeProgress * (containerWidth - 60);
            y = 0;
        } else if (progress < 0.5) {
            // Right edge, top to bottom
            const edgeProgress = (progress - 0.25) / 0.25;
            x = containerWidth - 60;
            y = edgeProgress * (containerHeight - 60);
        } else if (progress < 0.75) {
            // Bottom edge, right to left
            const edgeProgress = (progress - 0.5) / 0.25;
            x = (containerWidth - 60) * (1 - edgeProgress);
            y = containerHeight - 60;
        } else {
            // Left edge, bottom to top
            const edgeProgress = (progress - 0.75) / 0.25;
            x = 0;
            y = (containerHeight - 60) * (1 - edgeProgress);
        }
        
        img.style.position = 'absolute';
        img.style.left = x + 'px';
        img.style.top = y + 'px';
    }
    
    function animateFloatingImage2(elapsed) {
        const img = animations.floatingImage2.element;
        if (!img) return;
        
        const container = img.parentElement;
        if (!container) return;
        
        const duration = animations.floatingImage2.duration;
        const progress = (elapsed % duration) / duration;
        
        // Calculate orbit position (counter-clockwise)
        const containerRect = container.getBoundingClientRect();
        const containerWidth = containerRect.width;
        const containerHeight = containerRect.height;
        
        let x, y;
        
        if (progress < 0.25) {
            // Left edge, bottom to top
            const edgeProgress = progress / 0.25;
            x = 0;
            y = (containerHeight - 60) * (1 - edgeProgress);
        } else if (progress < 0.5) {
            // Top edge, left to right
            const edgeProgress = (progress - 0.25) / 0.25;
            x = edgeProgress * (containerWidth - 60);
            y = 0;
        } else if (progress < 0.75) {
            // Right edge, top to bottom
            const edgeProgress = (progress - 0.5) / 0.25;
            x = containerWidth - 60;
            y = edgeProgress * (containerHeight - 60);
        } else {
            // Bottom edge, right to left
            const edgeProgress = (progress - 0.75) / 0.25;
            x = (containerWidth - 60) * (1 - edgeProgress);
            y = containerHeight - 60;
        }
        
        img.style.position = 'absolute';
        img.style.left = x + 'px';
        img.style.top = y + 'px';
    }
    
    // Initialize based on document state
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', init);
    } else {
        init();
    }
    
    // Expose for debugging
    window.stableAnimations = {
        start: startAnimations,
        stop: stopAnimations,
        isVisible: () => isPageVisible,
        animations: animations
    };
    
})();

/* ===================================
   DECORATIVE IMAGES HANDLER
   ===================================*/

(function() {
    'use strict';
    
    let decorativeAnimations = [];
    let decorativeFrame;
    let decorativeStartTime = Date.now();
    let isDecorativeVisible = true;
    
    function initDecorativeImages() {
        const decorativeImages = document.querySelectorAll('.decorative-image');
        
        decorativeImages.forEach((img, index) => {
            decorativeAnimations.push({
                element: img,
                duration: 60000, // 60 seconds
                direction: index % 2 === 0 ? 'cw' : 'ccw',
                index: index
            });
            
            // Remove CSS animation
            img.style.animation = 'none';
        });
        
        startDecorativeAnimations();
    }
    
    function startDecorativeAnimations() {
        if (decorativeFrame) {
            cancelAnimationFrame(decorativeFrame);
        }
        
        function animate() {
            if (!isDecorativeVisible) return;
            
            const currentTime = Date.now();
            const elapsed = currentTime - decorativeStartTime;
            
            decorativeAnimations.forEach(anim => {
                animateDecorativeImage(anim, elapsed);
            });
            
            decorativeFrame = requestAnimationFrame(animate);
        }
        
        animate();
    }
    
    function animateDecorativeImage(anim, elapsed) {
        const img = anim.element;
        if (!img) return;
        
        const container = img.parentElement;
        if (!container) return;
        
        const duration = anim.duration;
        const progress = (elapsed % duration) / duration;
        
        const containerRect = container.getBoundingClientRect();
        const containerWidth = containerRect.width;
        const containerHeight = containerRect.height;
        
        let x, y;
        
        if (anim.direction === 'cw') {
            // Clockwise
            if (progress < 0.25) {
                const edgeProgress = progress / 0.25;
                x = edgeProgress * (containerWidth - 60);
                y = 0;
            } else if (progress < 0.5) {
                const edgeProgress = (progress - 0.25) / 0.25;
                x = containerWidth - 60;
                y = edgeProgress * (containerHeight - 60);
            } else if (progress < 0.75) {
                const edgeProgress = (progress - 0.5) / 0.25;
                x = (containerWidth - 60) * (1 - edgeProgress);
                y = containerHeight - 60;
            } else {
                const edgeProgress = (progress - 0.75) / 0.25;
                x = 0;
                y = (containerHeight - 60) * (1 - edgeProgress);
            }
        } else {
            // Counter-clockwise
            if (progress < 0.25) {
                const edgeProgress = progress / 0.25;
                x = 0;
                y = (containerHeight - 60) * (1 - edgeProgress);
            } else if (progress < 0.5) {
                const edgeProgress = (progress - 0.25) / 0.25;
                x = edgeProgress * (containerWidth - 60);
                y = 0;
            } else if (progress < 0.75) {
                const edgeProgress = (progress - 0.5) / 0.25;
                x = containerWidth - 60;
                y = edgeProgress * (containerHeight - 60);
            } else {
                const edgeProgress = (progress - 0.75) / 0.25;
                x = (containerWidth - 60) * (1 - edgeProgress);
                y = containerHeight - 60;
            }
        }
        
        img.style.position = 'absolute';
        img.style.left = x + 'px';
        img.style.top = y + 'px';
    }
    
    // Setup visibility for decorative images
    document.addEventListener('visibilitychange', function() {
        if (document.hidden) {
            isDecorativeVisible = false;
            if (decorativeFrame) {
                cancelAnimationFrame(decorativeFrame);
            }
        } else {
            isDecorativeVisible = true;
            decorativeStartTime = Date.now();
            startDecorativeAnimations();
        }
    });
    
    // Initialize decorative images
    setTimeout(initDecorativeImages, 1000);
    
})();
