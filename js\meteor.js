// Stable Meteor Animation - No Speed Issues
(function(){
  let meteorInterval;
  let isTabActive = true;

  function randomizeMeteor() {
    if (!isTabActive) return;

    const meteors = document.querySelectorAll('.meteor');
    meteors.forEach(meteor => {
      meteor.style.top = Math.random() * 8 + '%';
      meteor.style.left = Math.random() * 8 + '%';
      meteor.style.animationDelay = Math.random() * 1 + 's';
      // Keep original CSS animation for meteors as they're short
      meteor.style.animationPlayState = 'running';
    });
  }

  function stopMeteors() {
    if (meteorInterval) {
      clearInterval(meteorInterval);
      meteorInterval = null;
    }
  }

  function startMeteors() {
    if (meteorInterval) clearInterval(meteorInterval);

    if (document.querySelector('.meteor')) {
      randomizeMeteor();
      meteorInterval = setInterval(randomizeMeteor, 5000);
    }
  }

  // Handle page visibility - completely stop/start
  document.addEventListener('visibilitychange', function() {
    if (document.hidden) {
      isTabActive = false;
      stopMeteors();
      console.log('Meteors stopped - tab hidden');
    } else {
      isTabActive = true;
      startMeteors();
      console.log('Meteors started - tab visible');
    }
  });

  // Handle window focus/blur
  window.addEventListener('blur', function() {
    isTabActive = false;
    stopMeteors();
  });

  window.addEventListener('focus', function() {
    isTabActive = true;
    startMeteors();
  });

  // Initialize meteors
  startMeteors();
})();