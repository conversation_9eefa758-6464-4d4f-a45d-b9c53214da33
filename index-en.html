<!DOCTYPE html>
<html lang="en" dir="ltr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=5.0, user-scalable=yes, viewport-fit=cover">
    <title>MCT</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="css/style.css">
    <link rel="stylesheet" href="css/slider.css">
    <link rel="stylesheet" href="css/virtical-slider.css">
    <link rel="stylesheet" href="css/fix-overflow.css">
    <link rel="stylesheet" href="css/responsive.css">
    <link rel="stylesheet" href="css/services-mobile.css">
    <link rel="stylesheet" href="css/contact-footer-mobile.css">
    <link rel="stylesheet" href="css/mobile-fixes.css">
    <!-- إضافة مكتبة GSAP -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/gsap/3.13.0/gsap.min.js"></script>
    <script src="js/performance.js" defer></script>
    <script src="js/mobile-performance.js" defer></script>
    <script src="js/scroll-animations.js" defer></script>
    <script src="js/mobile-nav.js" defer></script>
    <script src="js/touch-slider.js" defer></script>
    <script src="js/common.js" defer></script>
</head>
<body>
    <!-- شاشة التحميل -->
    <div class="page-loader" id="pageLoader">
        <div class="loader-logo">
            <img src="image/logo-8.png" alt="MCT Logo">
            <div class="loader-text">Mojadedoon For Creative Technology</div>
        </div>
    </div>

    <!-- خلفية متحركة -->
    <div class="animated-bg"></div>
    <div class="floating-particles" id="particles"></div>
    <div class="meteor" id="meteor-1"></div>

    <!-- شريط التنقل -->
    <nav class="navbar">
        <div class="nav-container">
            <div class="logo-section">
                <div class="logo">
                    <img src="image/logo-11.png" alt="MCT Logo">
                </div>
                <div class="logo">
                    <img src="image/logo-10.png" alt="MCT Logo">
                </div>
            </div>
            <div class="nav-center">
                <button class="hamburger" id="hamburger-menu" aria-label="Open menu">
                    <span></span>
                    <span></span>
                    <span></span>
                </button>
                <ul class="nav-links" id="nav-links">
                    <li><a href="#home">Home</a></li>
                    <li><a href="#services">Services</a></li>
                    <li><a href="#contact">Contact Us</a></li>
                    <li></li>
                    <li></li>
                    <li></li>
                    <li><button id="lang-toggle" onclick="window.switchLanguage('ar')">AR</button></li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- المحتوى الرئيسي -->
    <main class="main-content content-loading" id="mainContent">
        <!-- الصفحة الرئيسية -->
        <section id="home" class="section">
            <div class="hero-section">
                <div class="hero-logo fade-in-element">
                    <img src="image/logo-8.png" alt="MCT Logo">
                </div>
                <div class="hero-content fade-in-element delay-1">
                    <p class="fade-in-element delay-2">
                       We are here to provide you with innovative technological solutions tailored to your needs. Whether you are looking for software solutions, web application development, or security solutions, our specialized team is ready to offer support and assistance. We believe that technology is the key to success and strive to deliver the best services to our clients.
                    </p>
                    <button class="cta-button fade-in-element delay-3" onclick="document.getElementById('services').scrollIntoView({behavior: 'smooth'})">Discover Our Services</button>
                </div>
            </div>
        </section>

        <!-- Slider (GSAP) -->
        <section class="slider-section">
            <div class="slider" id="slider">
                <div class="slides-wrapper" id="slidesWrapper">
                    <div class="slide active">
                        <img src="image/5.png" alt="Integrated Technical Solutions" />
                       <!--<div class="slide-content">
                            <h2>Integrated Technical Solutions</h2>
                            <p>We provide the latest integrated technical solutions to grow your business and help you succeed in the digital age</p>
                            <a href="#services" class="cta-button">Discover More</a>
                        </div>--> 
                    </div>
                    <div class="slide">
                        <img src="image/17.jpg" alt="Advanced Security Solutions" />
                       <!-- <div class="slide-content">
                            <h2>Advanced Security Solutions</h2>
                            <p>Comprehensive protection for your systems and data from electronic threats with the latest technologies</p>
                            <a href="cybersecurity-en.html" class="cta-button">Explore Security Services</a>
                        </div>-->
                    </div>
                    <div class="slide">
                        <img src="image/18.jpg" alt="Custom Software Development" />
                       <!-- <div class="slide-content">
                            <h2>Custom Software Development</h2>
                            <p>Design and develop innovative software solutions that precisely meet your business needs</p>
                            <a href="software-en.html" class="cta-button">Start Your Project</a>
                        </div>-->
                    </div>
                </div>
                <div class="slider-navigation">
                    <button class="arrow-btn prev-arrow" id="prevArrow">
                        <i class="fas fa-chevron-left"></i>
                    </button>
                    <div class="dots" id="dots"></div>
                    <button class="arrow-btn next-arrow" id="nextArrow">
                        <i class="fas fa-chevron-right"></i>
                    </button>
                </div>
            </div>
        </section>

        <!-- قسم الخدمات -->
        <section id="services" class="section">
            <div class="services-container">
                <div class="services-section">
                    <h2 class="section-title fade-in-element">Our Services</h2>
                    <div class="services-grid">
                        <a href="networking-en.html" class="service-card fade-in-element delay-1">
                            <div>
                                    <img src="image/1e.jpg" class="img-service"style="width: 250px; height: 290px;">
                                </div>
                            <h4>Network Solutions</h4>
                        </a>
                        <a href="software-en.html" class="service-card fade-in-element delay-1">
                                <div>
                                    <img src="image/12.jpeg" class="img-service"style="width: 250px; height: 290px;">
                                </div>
                            <h4>Software Development</h4>
                        </a>
                        <a href="cybersecurity-en.html" class="service-card fade-in-element delay-1">
                                <div>
                                    <img src="image/7e.jpg" class="img-service"style="width: 250px; height: 290px;">
                                </div>
                            <h4>Information Security</h4>
                        </a>
                        <a href="maintenance-en.html" class="service-card fade-in-element delay-1">
                                <div>
                                    <img src="image/14e.jpg" class="img-service"style="width: 250px; height: 290px;">
                                </div>
                            <h4>Support & Maintenance</h4>
                        </a>
                        
                    </div>
                </div>
            </div>
        </section>

        <!-- قسم السلايدر العامودي -->
        <section class="vertical-slider-section">
            <div class="decorative-image floating-image-1">
                <img src="image/programming.png" alt="C# Development">
            </div>
            <div class="decorative-image floating-image-2">
                <img src="image/networking.png" alt="Java Development">
            </div>
            <div class="vertical-content-container">
                <div class="vertical-text-content">
                    <div class="text-slide active">
                        <h3>Security First Approach</h3>
                        <p>In today's interconnected world, security is paramount. We implement robust security measures to protect your valuable data and ensure your business operations remain secure and uninterrupted.</p>
                    </div>
                    <div class="text-slide">
                        <h3>Excellence in Every Project</h3>
                        <p>From concept to completion, we maintain the highest standards of quality and professionalism. Every project we undertake reflects our commitment to excellence and customer satisfaction.</p>
                    </div>
                    <div class="text-slide">
                        <h3>24/24 Technical Support</h3>
                        <p>Our dedicated support team is available around the clock to address your technical needs and ensure your systems run smoothly at all times.</p>
                    </div>
                    <div class="text-slide">
                        <h3>Custom Solutions</h3>
                        <p>We understand that every business is unique. That's why we create tailored solutions that perfectly align with your specific requirements and business objectives.</p>
                    </div>
                </div>
                <div class="vertical-slider-container">
                    <div class="gallery-container">
                        <div class="gallery-wrapper">
                            <div class="gallery-grid">
                                 <div class="image-card">
                                    <img src="image/15.jpg" alt="Service Image 2">
                                </div>
                                <div class="image-card">
                                    <img src="image/10.jpg" alt="Service Image 2">
                                </div>
                                <div class="image-card">
                                    <img src="image/16.jpg" alt="Service Image 1">
                                </div>
                                <div class="image-card">
                                    <img src="image/13.jpg" alt="Service Image 2">
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>


    </main>

    <!-- تذييل الصفحة -->
    <footer class="footer" id="contact">
        <div class="footer-content">
            <a href="#home" class="footer-logo" >
             <img src="image/logo-8.png" alt="MCT Logo" >
            </a>
            <p>We provide innovative technical solutions that meet the needs of the digital age</p>

            <!-- معلومات التواصل -->
            <div class="footer-contact">
                <h3>Contact Us</h3>
                <div class="footer-contact-grid">
                    <div class="footer-contact-item">
                        <i class="fas fa-envelope"></i>
                        <div>
                            <h4>Email</h4>
                            <p><a href="mailto:<EMAIL>"><EMAIL></a></p>
                            <p><a href="mailto:<EMAIL>"><EMAIL></a></p>
                        </div>
                    </div>
                    <div class="footer-contact-item">
                        <i class="fas fa-phone"></i>
                        <div>
                            <h4>Phone Numbers</h4>
                            <p><a href="tel:+963951721454">+963 951 721 454</a></p>
                        </div>
                    </div>
                    <div class="footer-contact-item">
                        <i class="fas fa-map-marker-alt"></i>
                        <div>
                            <h4>Address</h4>
                            <p>Damascus</p>
                        </div>
                    </div>
                </div>
            </div>

            <div class="footer-links">
                <a href="#home">Home</a>
                <a href="#services">Services</a>
            </div>
        </div>
    </footer>

    <!-- زر الانتقال للأعلى -->
    <div class="scroll-top" id="scrollTop">
        <i class="fas fa-arrow-up"></i>
    </div>

    <script>
        // إنشاء الجسيمات المتحركة
        function createParticles() {
            const particlesContainer = document.getElementById('particles');
            const particleCount = 50;

            for (let i = 0; i < particleCount; i++) {
                const particle = document.createElement('div');
                particle.className = 'particle';
                particle.style.left = Math.random() * 100 + '%';
                particle.style.top = Math.random() * 100 + '%';
                particle.style.animationDelay = Math.random() * 6 + 's';
                particle.style.animationDuration = (Math.random() * 3 + 3) + 's';
                particlesContainer.appendChild(particle);
            }
        }

        // تأثير التمرير السلس
        function smoothScroll() {
            const navLinks = document.querySelectorAll('.nav-links a, .cta-button, .footer-links a');
            navLinks.forEach(link => {
                link.addEventListener('click', (e) => {
                    if (link.getAttribute('href').startsWith('#')) {
                        e.preventDefault();
                        const targetId = link.getAttribute('href');
                        const targetSection = document.querySelector(targetId);
                        if (targetSection) {
                            targetSection.scrollIntoView({
                                behavior: 'smooth'
                            });
                        }
                    }
                });
            });
        }

        // تأثير شريط التنقل عند التمرير
        function navbarScrollEffect() {
            const navbar = document.querySelector('.navbar');
            window.addEventListener('scroll', () => {
                if (window.scrollY > 100) {
                    navbar.style.background = 'rgba(15, 15, 35, 0.98)';
                    navbar.style.boxShadow = '0 10px 30px rgba(0, 0, 0, 0.3)';
                } else {
                    navbar.style.background = 'rgba(15, 15, 35, 0.95)';
                    navbar.style.boxShadow = 'none';
                }
            });
        }

        // تأثير الظهور التدريجي عند التمرير
        function scrollAnimations() {
            const observerOptions = {
                threshold: 0.1,
                rootMargin: '0px 0px -50px 0px'
            };

            const observer = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        entry.target.classList.add('visible');
                    }
                });
            }, observerOptions);

            // مراقبة جميع العناصر التي تحتوي على كلاس fade-in-element
            const fadeElements = document.querySelectorAll('.fade-in-element');
            fadeElements.forEach(element => {
                observer.observe(element);
            });
        }

        // زر الانتقال للأعلى
        function setupScrollTop() {
            const scrollTopBtn = document.getElementById('scrollTop');

            window.addEventListener('scroll', () => {
                if (window.scrollY > 500) {
                    scrollTopBtn.classList.add('visible');
                } else {
                    scrollTopBtn.classList.remove('visible');
                }
            });

            scrollTopBtn.addEventListener('click', () => {
                window.scrollTo({
                    top: 0,
                    behavior: 'smooth'
                });
            });
        }

        // تكرار حركة الشهاب
        function randomizeMeteor() {
            const meteors = document.querySelectorAll('.meteor');

            meteors.forEach(meteor => {
                meteor.style.top = Math.random() * 8 + '%';
                meteor.style.left = Math.random() * 8 + '%';
                meteor.style.animationDelay = Math.random() * 1 + 's';
            });
        }

        // تأثير التبديل السلس للغة
        function switchLanguage(lang) {
            if (lang === 'ar') {
                // إظهار شاشة التحميل
                const pageLoader = document.getElementById('pageLoader');
                pageLoader.style.display = 'flex';
                pageLoader.classList.remove('fade-out');

                // إضافة معرف للصفحة الجديدة
                sessionStorage.setItem('pageTransition', 'true');

                // تأخير قصير ثم الانتقال
                setTimeout(() => {
                    window.location.href = 'index.html';
                }, 500);
            }
        }

        // إضافة الدالة للنافذة العامة
        window.switchLanguage = switchLanguage;

        // تهيئة الموقع
        function initWebsite() {
            createParticles();
            smoothScroll();
            navbarScrollEffect();
            scrollAnimations();
            setupScrollTop();
            randomizeMeteor();
            enhanceServiceCards(); // إضافة تحسينات الخدمات
            initVerticalSlider(); // إضافة عرض عمودي
            setInterval(randomizeMeteor, 5000);
        }

        // Vertical slider (single image + single text, auto only)
        function initVerticalSlider() {
            const galleryWrapper = document.querySelector('.gallery-wrapper');
            const galleryGrid = document.querySelector('.gallery-grid');
            const imageCards = galleryGrid.querySelectorAll('.image-card');
            const textSlides = document.querySelectorAll('.text-slide');
            
            // تحقق من وجود العناصر قبل المتابعة
            if (!galleryWrapper || !galleryGrid || !imageCards.length || !textSlides.length) {
                console.warn('Vertical slider elements not found');
                return;
            }
            
            let currentIndex = 0;
            let autoScrollInterval;
            let autoScrollDelay = 4000; // 4 seconds

            // إظهار صورة واحدة وعبارة واحدة فقط (تطابق الصفحة العربية)
            function showSlide(index) {
                imageCards.forEach((card, i) => {
                    card.style.display = (i === index) ? 'block' : 'none';
                });
                textSlides.forEach((slide, i) => {
                    slide.classList.toggle('active', i === index);
                });
            }

            function nextSlide() {
                currentIndex = (currentIndex + 1) % imageCards.length;
                showSlide(currentIndex);
            }

            function startAutoScroll() {
                if (autoScrollInterval) clearInterval(autoScrollInterval);
                autoScrollInterval = setInterval(nextSlide, autoScrollDelay);
            }

            // تهيئة العرض الأول
            showSlide(currentIndex);
            startAutoScroll();
            
            // إيقاف التشغيل التلقائي عند إخفاء الصفحة
            document.addEventListener('visibilitychange', function() {
                if (document.visibilityState === 'hidden') {
                    clearInterval(autoScrollInterval);
                } else {
                    startAutoScroll();
                }
            });
        }

        // إظهار المحتوى مع التأثيرات
        function showContentWithAnimation() {
            const mainContent = document.getElementById('mainContent');

            // إظهار المحتوى الرئيسي بتأثير fade-in
            mainContent.classList.remove('content-loading');
            mainContent.classList.add('content-loaded');

            // إضافة تأثيرات للعناصر بالتتابع
            setTimeout(() => {
                addStaggerAnimation();
            }, 100);
        }

        // إضافة تأثير التتابع للعناصر
        function addStaggerAnimation() {
            const elements = [
                '.hero-section',
                '.services-section h2',
                '.service-card',
                '.contact-section'
            ];

            elements.forEach((selector, index) => {
                const items = document.querySelectorAll(selector);
                items.forEach((item, itemIndex) => {
                    setTimeout(() => {
                        item.classList.add('stagger-animation');
                    }, (index * 150) + (itemIndex * 80));
                });
            });
        }

        // إظهار شاشة الانتقال
        function showPageTransition(mainText, subText, callback) {
            // إنشاء عنصر الانتقال
            const transition = document.createElement('div');
            transition.className = 'page-transition';
            transition.innerHTML = `
                <div class="transition-content">
                    <div class="transition-logo">
                        <img src="image/logo-8.png" alt="MCT Logo" style="width: 100%; height: 100%; object-fit: contain;">
                    </div>
                    <div class="transition-text">${mainText}</div>
                    <div class="transition-subtext">${subText}</div>
                </div>
            `;

            document.body.appendChild(transition);

            // تفعيل الانتقال
            setTimeout(() => {
                transition.classList.add('active');
            }, 50);

            // تنفيذ الانتقال بعد التأثير
            setTimeout(() => {
                callback();
            }, 1200);
        }

        // تحسين تأثيرات hover للخدمات
        function enhanceServiceCards() {
            const serviceCards = document.querySelectorAll('.service-card');

            serviceCards.forEach(card => {
                // إضافة تأثير الماوس
                card.addEventListener('mouseenter', function() {
                    // إضافة تأثير صوتي بصري
                    this.style.transform = 'translateY(-10px) rotateX(5deg) scale(1.02)';

                    // تأثير الأيقونة
                    const icon = this.querySelector('.service-icon i');
                    if (icon) {
                        icon.style.animation = 'iconFloat 2s ease-in-out infinite';
                    }

                    // تأثير النص
                    const title = this.querySelector('h3');
                    const description = this.querySelector('p');

                    if (title) {
                        title.style.transform = 'translateX(5px)';
                        title.style.color = 'var(--accent-color)';
                    }

                    if (description) {
                        description.style.transform = 'translateX(3px)';
                        description.style.color = '#e0e0e0';
                    }
                });

                card.addEventListener('mouseleave', function() {
                    // إعادة تعيين التأثيرات
                    this.style.transform = 'translateY(0) rotateX(0) scale(1)';

                    const icon = this.querySelector('.service-icon i');
                    if (icon) {
                        icon.style.animation = '';
                    }

                    const title = this.querySelector('h3');
                    const description = this.querySelector('p');

                    if (title) {
                        title.style.transform = 'translateX(0)';
                        title.style.color = '';
                    }

                    if (description) {
                        description.style.transform = 'translateX(0)';
                        description.style.color = '';
                    }
                });

                // تأثير النقر
                card.addEventListener('click', function(e) {
                    // تأثير الموجة عند النقر
                    const ripple = document.createElement('div');
                    ripple.style.cssText = `
                        position: absolute;
                        border-radius: 50%;
                        background: rgba(0, 139, 128, 0.6);
                        transform: scale(0);
                        animation: ripple 0.6s linear;
                        pointer-events: none;
                    `;

                    const rect = this.getBoundingClientRect();
                    const size = Math.max(rect.width, rect.height);
                    const x = e.clientX - rect.left - size / 2;
                    const y = e.clientY - rect.top - size / 2;

                    ripple.style.width = ripple.style.height = size + 'px';
                    ripple.style.left = x + 'px';
                    ripple.style.top = y + 'px';

                    this.appendChild(ripple);

                    setTimeout(() => {
                        ripple.remove();
                    }, 600);
                });
            });
        }

        // تشغيل التهيئة عند تحميل الصفحة
        window.addEventListener('load', () => {
            handlePageLoad();
            initWebsite();
        });

        // إضافة مكتبة GSAP
        const slidesWrapper = document.getElementById('slidesWrapper');
        const slides = document.querySelectorAll('.slide');
        const dotsContainer = document.getElementById('dots');
        const prevArrow = document.getElementById('prevArrow');
        const nextArrow = document.getElementById('nextArrow');
        let currentIndex = 0;
        const totalSlides = slides.length;
        let animating = false;
        let autoPlayInterval;

        // Create navigation dots
        for(let i=0; i<totalSlides; i++) {
            const dot = document.createElement('div');
            dot.classList.add('dot');
            if(i === 0) dot.classList.add('active');
            dot.dataset.index = i;
            dotsContainer.appendChild(dot);
        }
        const dots = document.querySelectorAll('.dot');

        // Update dots
        function updateDots(index) {
            dots.forEach(dot => dot.classList.remove('active'));
            dots[index].classList.add('active');
        }

        // Update active slide
        function updateActiveSlide(index) {
            slides.forEach((slide, i) => {
                slide.classList.toggle('active', i === index);
            });
        }

        // GSAP slide transition (LTR)
        function goToSlide(index) {
            if(animating || index === currentIndex) return;
            animating = true;

            const currentSlide = slides[currentIndex];
            const nextSlide = slides[index];

            const currentTexts = currentSlide.querySelectorAll('.slide-content > *');
            const nextTexts = nextSlide.querySelectorAll('.slide-content > *');

            // Hide current slide texts
            gsap.to(currentTexts, {y: 30, opacity: 0, stagger: 0.05, duration: 0.2, ease: "power2.in"});

            // Move slides horizontally (LTR: negative offset) - Faster transition
            gsap.to(slidesWrapper, {
                x: -index * (slides[0].offsetWidth + 20) + 'px',
                duration: 0.5,
                ease: "power2.inOut",
                onComplete: () => {
                    // Show new slide texts
                    gsap.fromTo(nextTexts, 
                        {y: 30, opacity: 0}, 
                        {y: 0, opacity: 1, stagger: 0.1, duration: 0.3, ease: "power2.out"}
                    );
                    currentIndex = index;
                    updateDots(currentIndex);
                    updateActiveSlide(currentIndex);
                    animating = false;
                }
            });
        }

        // Dots navigation
        dots.forEach(dot => {
            dot.addEventListener('click', () => {
                const idx = parseInt(dot.dataset.index);
                goToSlide(idx);
                resetAutoPlay();
            });
        });

        // Arrow navigation
        prevArrow.addEventListener('click', () => {
            const prevIndex = currentIndex === 0 ? totalSlides - 1 : currentIndex - 1;
            goToSlide(prevIndex);
            resetAutoPlay();
        });

        nextArrow.addEventListener('click', () => {
            const nextIndex = (currentIndex + 1) % totalSlides;
            goToSlide(nextIndex);
            resetAutoPlay();
        });

        // Auto play
        function autoPlay() {
            autoPlayInterval = setInterval(() => {
                let nextIndex = (currentIndex + 1) % totalSlides;
                goToSlide(nextIndex);
            }, 4000);
        }
        function resetAutoPlay() {
            clearInterval(autoPlayInterval);
            autoPlay();
        }

        // Start auto play
        autoPlay();

        // Show first slide texts on load
        window.onload = () => {
            const firstTexts = slides[0].querySelectorAll('.slide-content > *');
            gsap.fromTo(firstTexts, 
                {y: 30, opacity: 0}, 
                {y: 0, opacity: 1, stagger: 0.15, duration: 0.6, ease: "power2.out"}
            );
        };

        // Update slides on resize
        window.addEventListener('resize', () => {
            gsap.set(slidesWrapper, {x: currentIndex * (slides[0].offsetWidth + 20)});
        });

        // تأكد من تشغيل السلايدر العمودي دائماً بعد تحميل الصفحة
        window.addEventListener('DOMContentLoaded', function() {
            if (typeof initVerticalSlider === 'function') {
                initVerticalSlider();
            }
        });

        // Hamburger menu functionality
        const hamburger = document.getElementById('hamburger-menu');
        const navLinks = document.querySelector('.nav-links');
        function toggleMenu() {
          navLinks.classList.toggle('open');
        }
        hamburger.addEventListener('click', toggleMenu);
        window.addEventListener('resize', function() {
          if(window.innerWidth > 900) navLinks.classList.remove('open');
        });
        // إظهار زر الهامبرغر فقط على الشاشات الصغيرة
        function updateHamburgerDisplay() {
          if(window.innerWidth <= 900) {
            hamburger.style.display = 'flex';
          } else {
            hamburger.style.display = 'none';
          }
        }
        window.addEventListener('resize', updateHamburgerDisplay);
        document.addEventListener('DOMContentLoaded', updateHamburgerDisplay);
    </script>
</body>
</html>
