/* ===================================
   SERVICES SECTION MOBILE OPTIMIZATION
   ===================================*/

/* Base Services Styles */
.services-section {
    padding: 60px 20px;
    text-align: center;
}

.section-title {
    font-size: 2.5rem;
    margin-bottom: 50px;
    color: var(--accent-color-1);
    text-align: center;
}

.services-grid {
    display: grid;
    grid-template-columns: 1fr;
    gap: 25px;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 15px;
    justify-items: center;
}

.service-card {
    background: var(--card-bg);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 20px;
    padding: 30px 20px;
    text-align: center;
    transition: all 0.3s ease;
    text-decoration: none;
    color: inherit;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    width: 300px;
    height: 400px;
    position: relative;
    overflow: hidden;
}

.service-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, transparent 0%, rgba(247, 166, 0, 0.05) 100%);
    opacity: 0;
    transition: opacity 0.3s ease;
    z-index: 1;
}

.service-card:hover::before {
    opacity: 1;
}

.service-card > * {
    position: relative;
    z-index: 2;
}

.service-card:hover {
    transform: translateY(-8px);
    background: rgba(255, 255, 255, 0.08);
    border-color: var(--accent-color-1);
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.2);
}

.service-card .img-service {
    width: 150px;
    height: 150px;
    object-fit: cover;
    border-radius: 15px;
    margin-bottom: 20px;
    transition: all 0.3s ease;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
}

.service-card:hover .img-service {
    transform: scale(1.1);
    box-shadow: 0 8px 25px rgba(247, 166, 0, 0.3);
}

.service-card h3 {
    font-size: 1.3rem;
    margin: 0;
    color: var(--accent-color-1);
    font-weight: 600;
    transition: all 0.3s ease;
}

.service-card:hover h3 {
    color: #fff;
    text-shadow: 0 2px 10px rgba(247, 166, 0, 0.5);
}

/* Tablet Styles */
@media (min-width: 768px) {
    .services-section {
        padding: 80px 40px;
    }
    
    .section-title {
        font-size: 2.8rem;
        margin-bottom: 60px;
    }
    
    .services-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 35px;
        padding: 0 20px;
        justify-items: center;
    }
    
    .service-card {
        padding: 35px 25px;
        width: 300px;
        height: 400px;
    }

    .service-card .img-service {
        width: 160px;
        height: 160px;
    }
    
    .service-card h3 {
        font-size: 1.4rem;
    }
}

/* Desktop Styles */
@media (min-width: 992px) {
    .services-section {
        padding: 100px 60px;
    }
    
    .section-title {
        font-size: 3rem;
        margin-bottom: 70px;
    }
    
    .services-grid {
        grid-template-columns: repeat(4, 1fr);
        gap: 40px;
        padding: 0 30px;
        justify-items: center;
    }
    
    .service-card {
        padding: 40px 30px;
        width: 300px;
        height: 400px;
    }

    .service-card .img-service {
        width: 170px;
        height: 170px;
    }
    
    .service-card h3 {
        font-size: 1.5rem;
    }
}

/* Large Desktop */
@media (min-width: 1200px) {
    .services-section {
        padding: 120px 80px;
    }
    
    .services-grid {
        gap: 50px;
        padding: 0 40px;
        justify-items: center;
    }
    
    .service-card {
        padding: 45px 35px;
        width: 300px;
        height: 400px;
    }
}

/* Mobile Specific Optimizations */
@media (max-width: 767px) {
    .services-section {
        padding: 50px 15px;
    }
    
    .section-title {
        font-size: 2.2rem;
        margin-bottom: 40px;
    }
    
    .services-grid {
        gap: 20px;
        padding: 0 10px;
    }
    
    .service-card {
        padding: 25px 20px;
        width: 280px;
        height: 350px;
        border-radius: 15px;
    }

    .service-card .img-service {
        width: 120px;
        height: 120px;
        border-radius: 12px;
        margin-bottom: 15px;
    }
    
    .service-card h3 {
        font-size: 1.2rem;
    }
    
    /* Enhanced touch targets for mobile */
    .service-card {
        width: 280px;
        height: 350px;
        touch-action: manipulation;
    }
    
    .service-card:active {
        transform: translateY(-4px) scale(0.98);
    }
}

/* Small Mobile */
@media (max-width: 480px) {
    .services-section {
        padding: 40px 10px;
    }
    
    .section-title {
        font-size: 2rem;
        margin-bottom: 30px;
    }
    
    .services-grid {
        gap: 15px;
        padding: 0 5px;
    }
    
    .service-card {
        padding: 20px 15px;
        width: 260px;
        height: 320px;
        border-radius: 12px;
    }

    .service-card .img-service {
        width: 100px;
        height: 100px;
        border-radius: 10px;
        margin-bottom: 12px;
    }
    
    .service-card h3 {
        font-size: 1.1rem;
    }
}

/* Accessibility Improvements */
@media (prefers-reduced-motion: reduce) {
    .service-card,
    .service-card .img-service,
    .service-card h3 {
        transition: none;
    }
    
    .service-card:hover {
        transform: none;
    }
    
    .service-card:hover .img-service {
        transform: none;
    }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
    .service-card {
        border: 2px solid rgba(255, 255, 255, 0.3);
    }
    
    .service-card:hover {
        border-color: var(--accent-color-1);
        background: rgba(255, 255, 255, 0.15);
    }
}

/* Dark mode optimizations */
@media (prefers-color-scheme: dark) {
    .service-card {
        background: rgba(255, 255, 255, 0.03);
        border-color: rgba(255, 255, 255, 0.08);
    }
    
    .service-card:hover {
        background: rgba(255, 255, 255, 0.06);
    }
}
