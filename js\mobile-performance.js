/* ===================================
   MOBILE PERFORMANCE OPTIMIZATIONS
   ===================================*/

document.addEventListener('DOMContentLoaded', function() {
    // Detect mobile device
    const isMobile = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
    const isTouch = 'ontouchstart' in window || navigator.maxTouchPoints > 0;
    
    // Add mobile class to body
    if (isMobile || isTouch) {
        document.body.classList.add('mobile-device');
    }
    
    // Optimize animations for mobile
    if (isMobile) {
        optimizeAnimationsForMobile();
    }
    
    // Lazy loading for images
    implementLazyLoading();
    
    // Throttle scroll events
    optimizeScrollEvents();
    
    // Optimize touch events
    optimizeTouchEvents();
    
    // Preload critical resources
    preloadCriticalResources();
    
    // Optimize font loading
    optimizeFontLoading();
});

/* ===================================
   ANIMATION OPTIMIZATIONS
   ===================================*/

function optimizeAnimationsForMobile() {
    // Only optimize UI transitions, never touch main animations
    const style = document.createElement('style');
    style.textContent = `
        @media (max-width: 767px) {
            /* Only optimize UI transition durations */
            .nav-links, .hamburger, .service-card, .cta-button {
                transition-duration: 0.3s !important;
            }

            /* Reduce particle complexity but don't hide completely */
            .floating-particles {
                opacity: 0.3 !important;
            }

            /* Keep meteors but reduce opacity */
            .meteor {
                opacity: 0.5 !important;
            }

            .animated-bg {
                background: linear-gradient(135deg, #0f0f23, #1a1a2e) !important;
            }
        }
    `;
    document.head.appendChild(style);
}

/* ===================================
   LAZY LOADING IMPLEMENTATION
   ===================================*/

function implementLazyLoading() {
    const images = document.querySelectorAll('img[src]');
    const imageObserver = new IntersectionObserver((entries, observer) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                const img = entry.target;
                
                // Add loading class
                img.classList.add('loading');
                
                // Create new image to preload
                const newImg = new Image();
                newImg.onload = function() {
                    img.src = this.src;
                    img.classList.remove('loading');
                    img.classList.add('loaded');
                };
                newImg.onerror = function() {
                    img.classList.remove('loading');
                    img.classList.add('error');
                };
                
                // Start loading
                if (img.dataset.src) {
                    newImg.src = img.dataset.src;
                } else {
                    img.classList.remove('loading');
                    img.classList.add('loaded');
                }
                
                observer.unobserve(img);
            }
        });
    }, {
        rootMargin: '50px 0px',
        threshold: 0.1
    });
    
    images.forEach(img => {
        // Add loading styles
        img.style.transition = 'opacity 0.3s ease';
        imageObserver.observe(img);
    });
}

/* ===================================
   SCROLL EVENT OPTIMIZATION
   ===================================*/

function optimizeScrollEvents() {
    let ticking = false;
    let lastScrollY = window.scrollY;
    
    function updateScrollEffects() {
        const currentScrollY = window.scrollY;
        const scrollDirection = currentScrollY > lastScrollY ? 'down' : 'up';
        
        // Update navbar on scroll
        updateNavbarOnScroll(currentScrollY, scrollDirection);
        
        // Update scroll-to-top button
        updateScrollToTop(currentScrollY);
        
        lastScrollY = currentScrollY;
        ticking = false;
    }
    
    function requestTick() {
        if (!ticking) {
            requestAnimationFrame(updateScrollEffects);
            ticking = true;
        }
    }
    
    // Use passive listeners for better performance
    window.addEventListener('scroll', requestTick, { passive: true });
}

function updateNavbarOnScroll(scrollY, direction) {
    const navbar = document.querySelector('.navbar');
    if (!navbar) return;
    
    if (scrollY > 100) {
        navbar.style.background = 'rgba(15, 15, 35, 0.98)';
        navbar.style.backdropFilter = 'blur(20px)';
        navbar.style.boxShadow = '0 10px 30px rgba(0, 0, 0, 0.3)';
    } else {
        navbar.style.background = 'rgba(15, 15, 35, 0.95)';
        navbar.style.backdropFilter = 'blur(10px)';
        navbar.style.boxShadow = 'none';
    }
    
    // Hide navbar on scroll down, show on scroll up (mobile only)
    if (window.innerWidth <= 767) {
        if (direction === 'down' && scrollY > 200) {
            navbar.style.transform = 'translateY(-100%)';
        } else if (direction === 'up') {
            navbar.style.transform = 'translateY(0)';
        }
    }
}

function updateScrollToTop(scrollY) {
    const scrollTopBtn = document.getElementById('scrollTop');
    if (!scrollTopBtn) return;
    
    if (scrollY > 500) {
        scrollTopBtn.classList.add('visible');
    } else {
        scrollTopBtn.classList.remove('visible');
    }
}

/* ===================================
   TOUCH EVENT OPTIMIZATIONS
   ===================================*/

function optimizeTouchEvents() {
    // Prevent zoom on double tap for specific elements
    const preventZoomElements = document.querySelectorAll('button, .service-card, .nav-links a');
    
    preventZoomElements.forEach(element => {
        element.addEventListener('touchend', function(e) {
            e.preventDefault();
            e.target.click();
        }, { passive: false });
    });
    
    // Add touch feedback
    addTouchFeedback();
    
    // Optimize touch scrolling
    optimizeTouchScrolling();
}

function addTouchFeedback() {
    const touchElements = document.querySelectorAll('button, .service-card, .nav-links a, .cta-button');
    
    touchElements.forEach(element => {
        element.addEventListener('touchstart', function() {
            this.classList.add('touch-active');
        }, { passive: true });
        
        element.addEventListener('touchend', function() {
            setTimeout(() => {
                this.classList.remove('touch-active');
            }, 150);
        }, { passive: true });
        
        element.addEventListener('touchcancel', function() {
            this.classList.remove('touch-active');
        }, { passive: true });
    });
    
    // Add CSS for touch feedback
    const style = document.createElement('style');
    style.textContent = `
        .touch-active {
            transform: scale(0.95) !important;
            opacity: 0.8 !important;
        }
    `;
    document.head.appendChild(style);
}

function optimizeTouchScrolling() {
    // Enable momentum scrolling on iOS
    document.body.style.webkitOverflowScrolling = 'touch';
    
    // Prevent overscroll bounce
    document.body.style.overscrollBehavior = 'contain';
}

/* ===================================
   RESOURCE PRELOADING
   ===================================*/

function preloadCriticalResources() {
    // Preload critical images
    const criticalImages = [
        'image/logo-8.png',
        'image/logo-10.png',
        'image/logo-11.png'
    ];
    
    criticalImages.forEach(src => {
        const link = document.createElement('link');
        link.rel = 'preload';
        link.as = 'image';
        link.href = src;
        document.head.appendChild(link);
    });
    
    // Preload critical CSS
    const criticalCSS = [
        'css/responsive.css',
        'css/services-mobile.css'
    ];
    
    criticalCSS.forEach(href => {
        const link = document.createElement('link');
        link.rel = 'preload';
        link.as = 'style';
        link.href = href;
        document.head.appendChild(link);
    });
}

/* ===================================
   FONT LOADING OPTIMIZATION
   ===================================*/

function optimizeFontLoading() {
    // Use font-display: swap for better performance
    const style = document.createElement('style');
    style.textContent = `
        @font-face {
            font-family: 'NotoNaskhArabic';
            font-display: swap;
        }
    `;
    document.head.appendChild(style);
}

/* ===================================
   VIEWPORT HEIGHT FIX FOR MOBILE
   ===================================*/

function fixMobileViewportHeight() {
    // Fix for mobile browsers that change viewport height
    const setVH = () => {
        const vh = window.innerHeight * 0.01;
        document.documentElement.style.setProperty('--vh', `${vh}px`);
    };
    
    setVH();
    window.addEventListener('resize', setVH);
    window.addEventListener('orientationchange', setVH);
}

// Initialize viewport height fix
fixMobileViewportHeight();

/* ===================================
   PERFORMANCE MONITORING
   ===================================*/

function monitorPerformance() {
    // Monitor page load performance
    window.addEventListener('load', function() {
        if ('performance' in window) {
            const perfData = performance.getEntriesByType('navigation')[0];
            console.log('Page Load Time:', perfData.loadEventEnd - perfData.loadEventStart, 'ms');
        }
    });
    
    // Monitor memory usage (if available)
    if ('memory' in performance) {
        setInterval(() => {
            const memory = performance.memory;
            if (memory.usedJSHeapSize > memory.jsHeapSizeLimit * 0.9) {
                console.warn('High memory usage detected');
            }
        }, 30000);
    }
}

// Initialize performance monitoring
monitorPerformance();
