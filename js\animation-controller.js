/* ===================================
   ANIMATION CONTROLLER - FIX TAB SWITCHING SPEED ISSUE
   ===================================*/

document.addEventListener('DOMContentLoaded', function() {
    let isTabActive = true;
    let animationElements = [];
    let originalAnimations = new Map();
    
    // Initialize animation controller
    initAnimationController();
    
    function initAnimationController() {
        // Collect all animated elements
        collectAnimatedElements();
        
        // Store original animation properties
        storeOriginalAnimations();
        
        // Set up visibility change listeners
        setupVisibilityListeners();
        
        // Set up page focus listeners
        setupFocusListeners();
    }
    
    function collectAnimatedElements() {
        // Hero logo
        const heroLogo = document.querySelector('.hero-logo');
        if (heroLogo) animationElements.push(heroLogo);
        
        // Floating images in vertical slider
        const floatingImages = document.querySelectorAll('.decorative-image, .floating-image-1, .floating-image-2');
        floatingImages.forEach(img => animationElements.push(img));
        
        // Meteors
        const meteors = document.querySelectorAll('.meteor');
        meteors.forEach(meteor => animationElements.push(meteor));
        
        // Floating particles
        const particles = document.querySelectorAll('.floating-particles, .particle');
        particles.forEach(particle => animationElements.push(particle));
        
        // Any other animated elements
        const otherAnimated = document.querySelectorAll('[class*="float"], [class*="rotate"], [class*="pulse"]');
        otherAnimated.forEach(element => animationElements.push(element));
        
        console.log('Found animated elements:', animationElements.length);
    }
    
    function storeOriginalAnimations() {
        animationElements.forEach(element => {
            const computedStyle = window.getComputedStyle(element);
            const animationName = computedStyle.animationName;
            const animationDuration = computedStyle.animationDuration;
            const animationTimingFunction = computedStyle.animationTimingFunction;
            const animationIterationCount = computedStyle.animationIterationCount;
            const animationDirection = computedStyle.animationDirection;
            const animationFillMode = computedStyle.animationFillMode;
            const animationPlayState = computedStyle.animationPlayState;
            
            originalAnimations.set(element, {
                name: animationName,
                duration: animationDuration,
                timingFunction: animationTimingFunction,
                iterationCount: animationIterationCount,
                direction: animationDirection,
                fillMode: animationFillMode,
                playState: animationPlayState
            });
        });
    }
    
    function setupVisibilityListeners() {
        // Handle page visibility change
        document.addEventListener('visibilitychange', function() {
            if (document.hidden) {
                // Page is hidden - pause animations
                pauseAllAnimations();
                isTabActive = false;
            } else {
                // Page is visible - resume animations
                resumeAllAnimations();
                isTabActive = true;
            }
        });
    }
    
    function setupFocusListeners() {
        // Handle window focus/blur
        window.addEventListener('blur', function() {
            pauseAllAnimations();
            isTabActive = false;
        });
        
        window.addEventListener('focus', function() {
            resumeAllAnimations();
            isTabActive = true;
        });
        
        // Handle page focus/blur
        window.addEventListener('pagehide', function() {
            pauseAllAnimations();
            isTabActive = false;
        });
        
        window.addEventListener('pageshow', function() {
            resumeAllAnimations();
            isTabActive = true;
        });
    }
    
    function pauseAllAnimations() {
        animationElements.forEach(element => {
            if (element && element.style) {
                element.style.animationPlayState = 'paused';
                element.classList.add('animation-paused');
            }
        });
        
        // Also pause CSS animations globally
        addGlobalAnimationPause();
    }
    
    function resumeAllAnimations() {
        animationElements.forEach(element => {
            if (element && element.style) {
                element.style.animationPlayState = 'running';
                element.classList.remove('animation-paused');
            }
        });
        
        // Remove global animation pause
        removeGlobalAnimationPause();
    }
    
    function addGlobalAnimationPause() {
        let pauseStyle = document.getElementById('animation-pause-style');
        if (!pauseStyle) {
            pauseStyle = document.createElement('style');
            pauseStyle.id = 'animation-pause-style';
            pauseStyle.textContent = `
                *, *::before, *::after {
                    animation-play-state: paused !important;
                }
                .animation-paused {
                    animation-play-state: paused !important;
                }
            `;
            document.head.appendChild(pauseStyle);
        }
    }
    
    function removeGlobalAnimationPause() {
        const pauseStyle = document.getElementById('animation-pause-style');
        if (pauseStyle) {
            pauseStyle.remove();
        }
    }
    
    // Reset animations when page becomes visible again
    function resetAnimations() {
        animationElements.forEach(element => {
            const original = originalAnimations.get(element);
            if (original && element.style) {
                element.style.animationName = original.name;
                element.style.animationDuration = original.duration;
                element.style.animationTimingFunction = original.timingFunction;
                element.style.animationIterationCount = original.iterationCount;
                element.style.animationDirection = original.direction;
                element.style.animationFillMode = original.fillMode;
                element.style.animationPlayState = 'running';
            }
        });
    }
    
    // Handle browser tab switching with Intersection Observer
    function setupIntersectionObserver() {
        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting && isTabActive) {
                    entry.target.style.animationPlayState = 'running';
                } else {
                    entry.target.style.animationPlayState = 'paused';
                }
            });
        }, {
            threshold: 0.1
        });
        
        animationElements.forEach(element => {
            observer.observe(element);
        });
    }
    
    // Initialize intersection observer
    setupIntersectionObserver();
    
    // Handle beforeunload to pause animations
    window.addEventListener('beforeunload', function() {
        pauseAllAnimations();
    });
    
    // Handle page load to ensure animations are running
    window.addEventListener('load', function() {
        setTimeout(() => {
            if (isTabActive) {
                resumeAllAnimations();
            }
        }, 100);
    });
    
    // Expose functions globally for debugging
    window.animationController = {
        pause: pauseAllAnimations,
        resume: resumeAllAnimations,
        reset: resetAnimations,
        isActive: () => isTabActive,
        elements: animationElements
    };
});

/* ===================================
   PERFORMANCE OPTIMIZATION
   ===================================*/

// Throttle animation updates for better performance
function throttleAnimationUpdates() {
    let animationFrame;
    
    function updateAnimations() {
        // Only update if tab is active
        if (document.visibilityState === 'visible') {
            // Update animation states
            const animatedElements = document.querySelectorAll('[style*="animation"]');
            animatedElements.forEach(element => {
                if (element.style.animationPlayState !== 'running') {
                    element.style.animationPlayState = 'running';
                }
            });
        }
        
        animationFrame = requestAnimationFrame(updateAnimations);
    }
    
    // Start the animation loop
    animationFrame = requestAnimationFrame(updateAnimations);
    
    // Clean up on page unload
    window.addEventListener('beforeunload', () => {
        if (animationFrame) {
            cancelAnimationFrame(animationFrame);
        }
    });
}

// Initialize performance optimization
document.addEventListener('DOMContentLoaded', throttleAnimationUpdates);

/* ===================================
   FALLBACK FOR OLDER BROWSERS
   ===================================*/

// Fallback for browsers that don't support Page Visibility API
if (typeof document.hidden === 'undefined') {
    // Use focus/blur as fallback
    let isWindowFocused = true;
    
    window.addEventListener('focus', function() {
        isWindowFocused = true;
        document.querySelectorAll('[style*="animation"]').forEach(element => {
            element.style.animationPlayState = 'running';
        });
    });
    
    window.addEventListener('blur', function() {
        isWindowFocused = false;
        document.querySelectorAll('[style*="animation"]').forEach(element => {
            element.style.animationPlayState = 'paused';
        });
    });
}
