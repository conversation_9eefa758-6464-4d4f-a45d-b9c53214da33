/* ===================================
   ANIMATION LOCK - ABSOLUTE PROTECTION
   THIS FILE LOCKS ANIMATION SPEEDS
   DO NOT MODIFY OR REMOVE
   ===================================*/

/* HERO LOGO - LOCKED AT 8 SECONDS */
.hero-logo {
    animation-name: heroFloat !important;
    animation-duration: 8s !important;
    animation-timing-function: ease-in-out !important;
    animation-iteration-count: infinite !important;
    animation-direction: alternate !important;
    animation-fill-mode: both !important;
    animation-play-state: running !important;
}

/* FLOATING IMAGE 1 - LOCKED AT 60 SECONDS */
.floating-image-1 {
    animation-name: edgeOrbitCW !important;
    animation-duration: 60s !important;
    animation-timing-function: linear !important;
    animation-iteration-count: infinite !important;
    animation-fill-mode: both !important;
    animation-play-state: running !important;
}

/* FLOATING IMAGE 2 - LOCKED AT 60 SECONDS */
.floating-image-2 {
    animation-name: edgeOrbitCCW !important;
    animation-duration: 60s !important;
    animation-timing-function: linear !important;
    animation-iteration-count: infinite !important;
    animation-fill-mode: both !important;
    animation-play-state: running !important;
}

/* DECORATIVE IMAGES - LOCKED AT 60 SECONDS */
.decorative-image {
    animation-duration: 60s !important;
    animation-timing-function: linear !important;
    animation-iteration-count: infinite !important;
    animation-fill-mode: both !important;
    animation-play-state: running !important;
}

.decorative-image:nth-child(odd) {
    animation-name: edgeOrbitCW !important;
}

.decorative-image:nth-child(even) {
    animation-name: edgeOrbitCCW !important;
}

/* PREVENT ANY EXTERNAL MODIFICATIONS */
.hero-logo,
.floating-image-1,
.floating-image-2,
.decorative-image {
    will-change: transform !important;
    backface-visibility: hidden !important;
}

/* MOBILE PROTECTION - SAME SPEEDS */
@media (max-width: 767px) {
    .hero-logo {
        animation-duration: 8s !important;
    }
    
    .floating-image-1,
    .floating-image-2,
    .decorative-image {
        animation-duration: 60s !important;
    }
}

/* TABLET PROTECTION - SAME SPEEDS */
@media (max-width: 1024px) {
    .hero-logo {
        animation-duration: 8s !important;
    }
    
    .floating-image-1,
    .floating-image-2,
    .decorative-image {
        animation-duration: 60s !important;
    }
}

/* OVERRIDE ANY PERFORMANCE OPTIMIZATIONS */
@media (prefers-reduced-motion: no-preference) {
    .hero-logo {
        animation: heroFloat 8s ease-in-out infinite alternate !important;
    }
    
    .floating-image-1 {
        animation: edgeOrbitCW 60s linear infinite !important;
    }
    
    .floating-image-2 {
        animation: edgeOrbitCCW 60s linear infinite !important;
    }
}

/* FORCE CORRECT TIMING ON ALL BROWSERS */
@supports (animation-duration: 1s) {
    .hero-logo {
        animation-duration: 8s !important;
    }
    
    .floating-image-1,
    .floating-image-2,
    .decorative-image {
        animation-duration: 60s !important;
    }
}

/* WEBKIT SPECIFIC PROTECTION */
@supports (-webkit-animation-duration: 1s) {
    .hero-logo {
        -webkit-animation-duration: 8s !important;
    }
    
    .floating-image-1,
    .floating-image-2,
    .decorative-image {
        -webkit-animation-duration: 60s !important;
    }
}

/* FIREFOX SPECIFIC PROTECTION */
@supports (-moz-animation-duration: 1s) {
    .hero-logo {
        -moz-animation-duration: 8s !important;
    }
    
    .floating-image-1,
    .floating-image-2,
    .decorative-image {
        -moz-animation-duration: 60s !important;
    }
}

/* PREVENT STYLE INJECTION ATTACKS */
.hero-logo[style*="animation-duration"],
.floating-image-1[style*="animation-duration"],
.floating-image-2[style*="animation-duration"],
.decorative-image[style*="animation-duration"] {
    animation-duration: inherit !important;
}

/* RESET ANY INLINE STYLES THAT MIGHT INTERFERE */
.hero-logo {
    animation-duration: 8s !important;
}

.floating-image-1,
.floating-image-2,
.decorative-image {
    animation-duration: 60s !important;
}
