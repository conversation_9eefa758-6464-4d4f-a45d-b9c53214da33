/* ===================================
   CONTACT & FOOTER MOBILE OPTIMIZATION
   ===================================*/

/* Contact Section Base Styles */
.contact-section {
    padding: 80px 20px;
    background: rgba(15, 15, 35, 0.5);
    backdrop-filter: blur(10px);
}

.contact-section .section-title {
    font-size: 2.5rem;
    margin-bottom: 50px;
    color: var(--accent-color-1);
    text-align: center;
}

.contact-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 40px 20px;
    background: var(--card-bg);
    border-radius: 25px;
    border: 1px solid rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(20px);
}

.contact-grid {
    display: grid;
    grid-template-columns: 1fr;
    gap: 30px;
}

.contact-item {
    text-align: center;
    padding: 25px 20px;
    background: rgba(255, 255, 255, 0.03);
    border-radius: 15px;
    border: 1px solid rgba(255, 255, 255, 0.05);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.contact-item::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, transparent 0%, rgba(247, 166, 0, 0.05) 100%);
    opacity: 0;
    transition: opacity 0.3s ease;
}

.contact-item:hover::before {
    opacity: 1;
}

.contact-item:hover {
    transform: translateY(-5px);
    background: rgba(255, 255, 255, 0.06);
    border-color: var(--accent-color-1);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
}

.contact-item > * {
    position: relative;
    z-index: 2;
}

.contact-item h4 {
    color: var(--accent-color-1);
    margin-bottom: 15px;
    font-size: 1.2rem;
    font-weight: 600;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
}

.contact-item h4 i {
    font-size: 1.3rem;
    color: var(--accent-color-2);
}

.contact-item p {
    margin: 8px 0;
    color: #e0e0e0;
    font-size: 1rem;
    line-height: 1.6;
}

.contact-item a {
    color: #e0e0e0;
    text-decoration: none;
    transition: all 0.3s ease;
    padding: 5px 10px;
    border-radius: 8px;
    display: inline-block;
}

.contact-item a:hover {
    color: var(--accent-color-1);
    background: rgba(247, 166, 0, 0.1);
    transform: translateY(-2px);
}

/* Footer Base Styles */
.footer {
    background: linear-gradient(135deg, #0f0f23 0%, #1a1a2e 100%);
    padding: 50px 20px 30px;
    text-align: center;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    margin-top: 0;
}

.footer-content {
    max-width: 800px;
    margin: 0 auto;
}

.footer-logo {
    display: inline-block;
    width: 80px;
    height: 80px;
    margin-bottom: 25px;
    transition: transform 0.3s ease;
}

.footer-logo:hover {
    transform: scale(1.1) rotate(5deg);
}

.footer-logo img {
    width: 100%;
    height: 100%;
    object-fit: contain;
}

.footer-content p {
    color: #b0b0b0;
    font-size: 1.1rem;
    line-height: 1.6;
    margin-bottom: 30px;
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
}

.footer-links {
    display: flex;
    justify-content: center;
    gap: 20px;
    margin-bottom: 30px;
    flex-wrap: wrap;
}

.footer-links a {
    color: #e0e0e0;
    text-decoration: none;
    padding: 10px 20px;
    border-radius: 25px;
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
    transition: all 0.3s ease;
    font-weight: 500;
}

.footer-links a:hover {
    background: var(--accent-color-1);
    color: #fff;
    transform: translateY(-3px);
    box-shadow: 0 8px 20px rgba(247, 166, 0, 0.3);
}

/* Footer Contact Section */
.footer-contact {
    margin: 40px 0;
    padding: 30px 0;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.footer-contact h3 {
    color: var(--accent-color-1);
    font-size: 1.8rem;
    margin-bottom: 30px;
    text-align: center;
    font-weight: 600;
}

.footer-contact-grid {
    display: grid;
    grid-template-columns: 1fr;
    gap: 25px;
    max-width: 800px;
    margin: 0 auto;
}

.footer-contact-item {
    display: flex;
    align-items: flex-start;
    gap: 15px;
    padding: 20px;
    background: rgba(255, 255, 255, 0.03);
    border-radius: 12px;
    border: 1px solid rgba(255, 255, 255, 0.05);
    transition: all 0.3s ease;
}

.footer-contact-item:hover {
    background: rgba(255, 255, 255, 0.06);
    border-color: var(--accent-color-1);
    transform: translateY(-3px);
}

.footer-contact-item i {
    font-size: 1.5rem;
    color: var(--accent-color-1);
    margin-top: 5px;
    min-width: 24px;
}

.footer-contact-item div {
    flex: 1;
}

.footer-contact-item h4 {
    color: #fff;
    font-size: 1.1rem;
    margin-bottom: 8px;
    font-weight: 600;
}

.footer-contact-item p {
    margin: 4px 0;
    color: #e0e0e0;
    font-size: 0.95rem;
}

.footer-contact-item a {
    color: #e0e0e0;
    text-decoration: none;
    transition: color 0.3s ease;
}

.footer-contact-item a:hover {
    color: var(--accent-color-1);
}



/* Tablet Styles */
@media (min-width: 768px) {
    .contact-section {
        padding: 100px 40px;
    }
    
    .contact-section .section-title {
        font-size: 2.8rem;
        margin-bottom: 60px;
    }
    
    .contact-container {
        padding: 50px 40px;
    }
    
    .contact-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 40px;
    }
    
    .contact-item {
        padding: 30px 25px;
    }
    
    .contact-item h4 {
        font-size: 1.3rem;
    }
    
    .footer {
        padding: 60px 40px 40px;
    }
    
    .footer-logo {
        width: 90px;
        height: 90px;
    }
    
    .footer-content p {
        font-size: 1.2rem;
    }

    .footer-contact-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 30px;
    }

    .footer-contact h3 {
        font-size: 2rem;
    }
}

/* Desktop Styles */
@media (min-width: 992px) {
    .contact-section {
        padding: 120px 60px;
    }
    
    .contact-section .section-title {
        font-size: 3rem;
        margin-bottom: 70px;
    }
    
    .contact-container {
        padding: 60px 50px;
    }
    
    .contact-grid {
        grid-template-columns: repeat(3, 1fr);
        gap: 50px;
    }
    
    .contact-item {
        padding: 35px 30px;
    }
    
    .contact-item h4 {
        font-size: 1.4rem;
    }
    
    .footer {
        padding: 80px 60px 50px;
    }
    
    .footer-logo {
        width: 100px;
        height: 100px;
    }

    .footer-contact-grid {
        grid-template-columns: repeat(3, 1fr);
        gap: 40px;
    }

    .footer-contact h3 {
        font-size: 2.2rem;
    }
}

/* Mobile Specific Optimizations */
@media (max-width: 767px) {
    .contact-section {
        padding: 60px 15px;
    }
    
    .contact-section .section-title {
        font-size: 2.2rem;
        margin-bottom: 40px;
    }
    
    .contact-container {
        padding: 30px 20px;
        border-radius: 20px;
    }
    
    .contact-grid {
        gap: 20px;
    }
    
    .contact-item {
        padding: 20px 15px;
        border-radius: 12px;
    }
    
    .contact-item h4 {
        font-size: 1.1rem;
        flex-direction: column;
        gap: 5px;
    }
    
    .contact-item h4 i {
        font-size: 1.5rem;
    }
    
    .contact-item p {
        font-size: 0.95rem;
    }
    
    .footer {
        padding: 40px 15px 25px;
    }
    
    .footer-logo {
        width: 70px;
        height: 70px;
        margin-bottom: 20px;
    }
    
    .footer-content p {
        font-size: 1rem;
        margin-bottom: 25px;
    }
    
    .footer-links {
        flex-direction: column;
        gap: 10px;
        margin-bottom: 25px;
    }
    
    .footer-links a {
        padding: 12px 20px;
        width: 100%;
        max-width: 250px;
        margin: 0 auto;
    }
}

/* Small Mobile */
@media (max-width: 480px) {
    .contact-section {
        padding: 50px 10px;
    }
    
    .contact-section .section-title {
        font-size: 2rem;
        margin-bottom: 30px;
    }
    
    .contact-container {
        padding: 25px 15px;
        border-radius: 15px;
    }
    
    .contact-grid {
        gap: 15px;
    }
    
    .contact-item {
        padding: 18px 12px;
        border-radius: 10px;
    }
    
    .contact-item h4 {
        font-size: 1rem;
    }
    
    .contact-item p {
        font-size: 0.9rem;
    }
    
    .footer {
        padding: 35px 10px 20px;
    }
    
    .footer-logo {
        width: 60px;
        height: 60px;
        margin-bottom: 15px;
    }
    
    .footer-content p {
        font-size: 0.95rem;
        margin-bottom: 20px;
    }
    
    .footer-links a {
        padding: 10px 15px;
        font-size: 0.9rem;
    }
}

/* Accessibility Improvements */
@media (prefers-reduced-motion: reduce) {
    .contact-item,
    .footer-logo,
    .footer-links a {
        transition: none;
    }
    
    .contact-item:hover,
    .footer-logo:hover,
    .footer-links a:hover {
        transform: none;
    }
}

/* High contrast mode */
@media (prefers-contrast: high) {
    .contact-item,
    .contact-container {
        border: 2px solid rgba(255, 255, 255, 0.3);
    }
    
    .contact-item:hover {
        border-color: var(--accent-color-1);
    }
}
