/* ===================================
   SERVICE PAGES NAVBAR STYLES
   ===================================*/

/* Apply to all service pages */
body.service-page .navbar,
body:not(.main-page) .navbar {
    background: rgba(15, 15, 35, 0.95);
    backdrop-filter: blur(15px);
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.service-page .nav-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

.service-page .logo-section {
    order: 1;
    flex-shrink: 0;
}

.service-page .nav-center {
    order: 2;
    margin-left: auto;
}

/* Desktop Service Pages Navbar */
@media (min-width: 992px) {
    .service-page .nav-links {
        display: flex;
        flex-direction: row;
        gap: 25px;
        background: transparent;
        position: static;
        padding: 0;
        margin: 0;
        box-shadow: none;
    }
    
    .service-page .hamburger {
        display: none;
    }
    
    .service-page .nav-links li {
        margin: 0;
    }
    
    .service-page .nav-links a {
        padding: 10px 20px;
        border-radius: 25px;
        transition: all 0.3s ease;
        background: rgba(255, 255, 255, 0.05);
        border: 1px solid rgba(255, 255, 255, 0.1);
    }
    
    .service-page .nav-links a:hover {
        background: var(--accent-color-1);
        color: #fff;
        transform: translateY(-2px);
    }
    
    .service-page #lang-toggle {
        margin-left: 20px;
        padding: 8px 16px;
        background: var(--accent-color-1);
        color: #fff;
        border: none;
        border-radius: 20px;
        cursor: pointer;
        transition: all 0.3s ease;
    }
    
    .service-page #lang-toggle:hover {
        background: var(--accent-color-2);
        transform: scale(1.05);
    }
}

/* Mobile Service Pages Navbar */
@media (max-width: 991px) {
    .service-page .navbar {
        padding: 10px 15px;
    }
    
    .service-page .hamburger {
        display: flex !important;
        order: 3;
        margin-left: 15px;
    }
    
    .service-page .nav-links {
        display: none;
        position: fixed;
        top: 70px;
        right: 0;
        width: 280px;
        height: calc(100vh - 70px);
        flex-direction: column;
        background: rgba(15, 15, 35, 0.98);
        backdrop-filter: blur(20px);
        padding: 30px 20px;
        margin: 0;
        box-shadow: -10px 0 30px rgba(0, 0, 0, 0.3);
        z-index: 999;
        transform: translateX(100%);
        transition: transform 0.3s ease;
    }
    
    .service-page .nav-links.open {
        display: flex !important;
        transform: translateX(0);
    }
    
    .service-page .nav-links li {
        width: 100%;
        margin: 8px 0;
    }
    
    .service-page .nav-links a {
        display: block;
        width: 100%;
        padding: 15px 20px;
        text-align: right;
        border-radius: 10px;
        background: rgba(255, 255, 255, 0.05);
        border: 1px solid rgba(255, 255, 255, 0.1);
        transition: all 0.3s ease;
    }
    
    .service-page .nav-links a:hover {
        background: var(--accent-color-1);
        color: #fff;
        transform: translateX(-5px);
    }
    
    .service-page #lang-toggle {
        margin: 20px auto 0;
        padding: 12px 24px;
        background: var(--accent-color-1);
        color: #fff;
        border: none;
        border-radius: 25px;
        cursor: pointer;
        transition: all 0.3s ease;
        display: block;
        width: fit-content;
    }
    
    .service-page #lang-toggle:hover {
        background: var(--accent-color-2);
        transform: scale(1.05);
    }
}

/* Arabic Service Pages Specific */
.service-page[dir="rtl"] .nav-links {
    right: auto;
    left: 0;
    transform: translateX(-100%);
}

.service-page[dir="rtl"] .nav-links.open {
    transform: translateX(0);
}

.service-page[dir="rtl"] .nav-links a {
    text-align: left;
}

.service-page[dir="rtl"] .nav-links a:hover {
    transform: translateX(5px);
}

.service-page[dir="rtl"] .hamburger {
    order: 1;
    margin-right: 15px;
    margin-left: 0;
}

.service-page[dir="rtl"] .logo-section {
    order: 3;
}

/* Tablet Service Pages */
@media (min-width: 768px) and (max-width: 991px) {
    .service-page .nav-links {
        width: 320px;
        padding: 40px 30px;
    }
    
    .service-page .nav-links a {
        padding: 18px 25px;
        font-size: 1.1rem;
    }
}

/* Small Mobile Service Pages */
@media (max-width: 480px) {
    .service-page .navbar {
        padding: 8px 10px;
    }
    
    .service-page .nav-links {
        width: 100vw;
        right: 0;
        padding: 20px 15px;
    }
    
    .service-page .nav-links a {
        padding: 12px 15px;
        font-size: 0.95rem;
    }
    
    .service-page[dir="rtl"] .nav-links {
        left: 0;
    }
}

/* Service Page Overlay */
.service-page .nav-overlay {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    background: rgba(0, 0, 0, 0.5);
    z-index: 998;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.service-page .nav-overlay.active {
    display: block;
    opacity: 1;
}

/* Service Page Hamburger Animation */
.service-page .hamburger.active span:nth-child(1) {
    transform: rotate(-45deg) translate(-5px, 6px);
}

.service-page .hamburger.active span:nth-child(2) {
    opacity: 0;
}

.service-page .hamburger.active span:nth-child(3) {
    transform: rotate(45deg) translate(-5px, -6px);
}

/* Service Page Content Adjustment */
.service-page .main-content {
    padding-top: 90px;
}

@media (max-width: 991px) {
    .service-page .main-content {
        padding-top: 70px;
    }
}

/* Service Page Breadcrumb */
.service-page .breadcrumb {
    background: rgba(255, 255, 255, 0.05);
    padding: 15px 20px;
    margin-bottom: 30px;
    border-radius: 10px;
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.service-page .breadcrumb a {
    color: var(--accent-color-1);
    text-decoration: none;
    transition: color 0.3s ease;
}

.service-page .breadcrumb a:hover {
    color: #fff;
}

.service-page .breadcrumb span {
    color: #888;
    margin: 0 10px;
}

/* Performance Optimizations */
@media (max-width: 991px) {
    .service-page .nav-links {
        will-change: transform;
        backface-visibility: hidden;
        -webkit-backface-visibility: hidden;
    }
    
    .service-page .nav-overlay {
        will-change: opacity;
    }
}
