/* ===================================
   SLIDER STYLES - ISOLATED
   ===================================*/

/* Font Application for Slider */
* {
    font-family: 'NotoNaskhArabic', 'Arial', sans-serif;
}

.slider {
    position: relative;
    width: 80%;
    max-width: 800px;
    height: 450px;
    margin: 100px auto;
    overflow: visible;
    border-radius: 25px;
    margin-top: 0;
}

.slides-wrapper {
    display: flex;
    height: 100%;
    width: 100%;
    will-change: transform;
    cursor: grab;
    padding-left: 5%;
    padding-right: 5%;
    box-sizing: content-box;
    transform: translateX(0);
    transition: transform 1s cubic-bezier(0.77, 0, 0.175, 1);
}

.slide {
    flex: 0 0 80%;
    height: 100%;
    position: relative;
    overflow: hidden;
    border-radius: 15px;
    margin: 0 40px;
    transition: transform 0.4s ease;
    box-shadow: 0 0 15px rgba(0,0,0,0.5);
    background: #000;
    user-select: none;
}

.slide img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    filter: brightness(0.6);
    border-radius: 15px;
    pointer-events: none;
    user-select: none;
    transition: all 0.5s ease;
}

.slide:hover img {
    filter: brightness(1.0);
}

@keyframes brightenImage {
    0% {
        filter: brightness(0.6);
    }
    100% {
        filter: brightness(0.9);
    }
}

.slide-content {
    position: absolute;
    bottom: 5%;
    left: 50%;
    transform: translateX(-50%);
    color: #fff;
    max-width: 80%;
    text-align: center;
    z-index: 2;
    pointer-events: none;
}

.slide-content h2 {
    font-size: 2.8rem;
    margin-bottom: 15px;
    letter-spacing: 1.2px;
    opacity: 0;
    color: var(--accent-color-1);
}

.slide-content p {
    font-size: 1.1rem;
    line-height: 1.5;
    margin-bottom: 25px;
    opacity: 0;
    color: #e0e0e0;
}

.slide-content a {
    display: inline-block;
    padding: 12px 30px;
    background: var(--accent-color-1);
    color: white;
    text-decoration: none;
    font-weight: 600;
    border-radius: 30px;
    opacity: 0;
    box-shadow: 0 5px 15px rgba(247, 166, 0, 0.3);
    pointer-events: auto;
    transition: all 0.3s ease;
}

.slide-content a:hover {
    background: var(--accent-color-2);
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(247, 166, 0, 0.4);
}

.slide.active {
    transform: scale(1.20);
    box-shadow: 0 0 25px rgba(24, 20, 20, 0.9);
    z-index: 5;
}

.slide.active img {
    transform: scale(1);
    filter: brightness(0.9);
    animation: brightenImage 0.8s ease-in-out;
}

.slide:not(.active) {
    filter: brightness(0.7);
}

.slide:not(.active) img {
    filter: brightness(0.6);
}

/* Slider Navigation */
.slider-navigation {
    position: static;
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 20px;
    margin: 100px auto 0 auto;
    width: 100%;
    max-width: 1000px;
    height: 40px;
    z-index: 10;
    user-select: none;
}

.dots {
    display: flex;
    gap: 18px;
    align-items: center;
}

.dot {
    width: 14px;
    height: 14px;
    border-radius: 50%;
    background: rgba(255,255,255,0.4);
    cursor: pointer;
    transition: background 0.3s ease, transform 0.3s;
}

.dot.active {
    background: var(--accent-color-1);
    box-shadow: 0 0 8px var(--accent-color-1);
    transform: scale(1.2);
}

.arrow-btn {
    width: 45px;
    height: 45px;
    border: none;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.1);
    color: white;
    font-size: 18px;
    cursor: pointer;
    transition: all 0.3s ease;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    display: flex;
    align-items: center;
    justify-content: center;
}

.arrow-btn:hover {
    background: var(--accent-color-1);
    transform: scale(1.1);
    box-shadow: 0 5px 15px rgba(247, 166, 0, 0.3);
}

.arrow-btn:active {
    transform: scale(0.95);
}

/* Responsive Design */
@media (max-width: 768px) {
    .slider {
        width: 95%;
        height: 300px;
        margin: 30px auto;
    }

    .slide-content {
        max-width: 90%;
        bottom: 10px;
    }

    .slide-content h2 {
        font-size: 1.6rem;
        margin-bottom: 8px;
    }

    .slide-content p {
        font-size: 0.9rem;
        margin-bottom: 12px;
    }

    .slide {
        flex: 0 0 90%;
        margin: 0 5px;
        border-radius: 12px;
    }

    .slide img {
        border-radius: 12px;
    }

    .slides-wrapper {
        padding-left: 5%;
        padding-right: 5%;
    }

    .slider-navigation {
        margin-top: 40px;
        gap: 15px;
    }

    .arrow-btn {
        width: 44px;
        height: 44px;
        font-size: 18px;
        background: rgba(255, 255, 255, 0.2);
        border: 2px solid rgba(255, 255, 255, 0.3);
    }

    .dot {
        width: 14px;
        height: 14px;
    }
}

/* Android specific optimizations */
@media (max-width: 480px) {
    .slider {
        width: 95%;
        height: 280px;
        margin: 25px auto;
        border-radius: 15px;
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
    }

    .slide {
        flex: 0 0 90%;
        margin: 0 5px;
        border-radius: 12px;
    }

    .slide img {
        border-radius: 12px;
        object-fit: cover;
        object-position: center;
    }

    .slider-navigation {
        margin-top: 35px;
        gap: 15px;
        display: flex;
        justify-content: center;
        align-items: center;
    }

    .arrow-btn {
        width: 44px;
        height: 44px;
        font-size: 18px;
        background: rgba(255, 255, 255, 0.2);
        border: 2px solid rgba(255, 255, 255, 0.3);
        border-radius: 50%;
        color: #fff;
        cursor: pointer;
        transition: all 0.3s ease;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .arrow-btn:hover {
        background: var(--accent-color-1);
        border-color: var(--accent-color-1);
        transform: scale(1.1);
    }

    .dot {
        width: 14px;
        height: 14px;
        border-radius: 50%;
        background: rgba(255, 255, 255, 0.4);
        border: 2px solid rgba(255, 255, 255, 0.6);
        cursor: pointer;
        transition: all 0.3s ease;
    }

    .dot.active {
        background: var(--accent-color-1);
        border-color: var(--accent-color-1);
        box-shadow: 0 0 10px var(--accent-color-1);
    }

    .dots {
        display: flex;
        gap: 12px;
        align-items: center;
    }
}

/* Very small Android devices */
@media (max-width: 360px) {
    .slider {
        width: 98%;
        height: 250px;
        margin: 20px auto;
    }

    .arrow-btn {
        width: 40px;
        height: 40px;
        font-size: 16px;
    }

    .dot {
        width: 12px;
        height: 12px;
    }
}

@media (max-width: 480px) {
    .slider {
        width: 98%;
        height: 280px;
        margin: 30px auto;
    }

    .slide-content {
        max-width: 95%;
        bottom: 5px;
    }

    .slide-content h2 {
        font-size: 1.3rem;
        margin-bottom: 8px;
    }

    .slide-content p {
        font-size: 0.85rem;
        margin-bottom: 12px;
        line-height: 1.4;
    }

    .slide {
        flex: 0 0 90%;
        margin: 0 5px;
        border-radius: 10px;
    }

    .slide img {
        border-radius: 10px;
    }

    .slides-wrapper {
        padding-left: 5%;
        padding-right: 5%;
    }

    .slider-navigation {
        margin-top: 40px;
        gap: 12px;
    }

    .arrow-btn {
        width: 38px;
        height: 38px;
        font-size: 14px;
    }

    .dot {
        width: 10px;
        height: 10px;
    }

    .dots {
        gap: 12px;
    }
}

/* Touch-specific improvements */
@media (hover: none) and (pointer: coarse) {
    .slide {
        cursor: default;
    }

    .slides-wrapper {
        cursor: default;
    }

    .arrow-btn:hover {
        transform: none;
        background: var(--accent-color-1);
    }

    .dot:hover {
        transform: none;
    }

    /* Larger touch targets */
    .arrow-btn {
        min-width: 44px;
        min-height: 44px;
    }

    .dot {
        min-width: 44px;
        min-height: 44px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .dot::before {
        content: '';
        width: 12px;
        height: 12px;
        border-radius: 50%;
        background: rgba(255,255,255,0.4);
        transition: background 0.3s ease;
    }

    .dot.active::before {
        background: var(--accent-color-1);
        box-shadow: 0 0 8px var(--accent-color-1);
    }
}