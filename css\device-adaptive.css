/* ===================================
   DEVICE ADAPTIVE STYLES
   ===================================*/

/* Base styles for all devices */
* {
    box-sizing: border-box;
}

/* Mobile First Approach */
.container {
    width: 100%;
    padding: 0 15px;
    margin: 0 auto;
}

/* Adaptive animations and shapes */
.animated-element {
    transition: all 0.3s ease;
}

/* Mobile Devices (320px - 767px) */
@media (max-width: 767px) {
    .container {
        padding: 0 10px;
    }
    
    /* Navbar adaptations */
    .navbar {
        padding: 8px 15px;
        height: 60px;
    }
    
    .logo {
        width: 35px;
        height: 35px;
    }
    
    /* Hero section adaptations */
    .hero-section {
        padding: 80px 15px 40px;
        min-height: calc(100vh - 60px);
    }
    
    .hero-logo {
        width: 100px;
        height: 100px;
    }
    
    /* Services adaptations */
    .services-section {
        padding: 40px 15px;
    }
    
    .service-card {
        width: 280px;
        height: 350px;
        margin: 10px auto;
    }
    
    /* Slider adaptations */
    .slider-section {
        padding: 20px 10px;
    }
    
    /* Footer adaptations */
    .footer {
        padding: 30px 15px;
    }
    
    .footer-contact-grid {
        grid-template-columns: 1fr;
        gap: 15px;
    }
    
    /* Animations for mobile */
    .animated-element {
        animation-duration: 0.3s !important;
    }
    
    /* Touch optimizations */
    button, .clickable {
        min-height: 44px;
        min-width: 44px;
    }
}

/* Tablet Portrait (768px - 991px) */
@media (min-width: 768px) and (max-width: 991px) {
    .container {
        max-width: 750px;
        padding: 0 20px;
    }
    
    .navbar {
        padding: 10px 20px;
        height: 70px;
    }
    
    .logo {
        width: 40px;
        height: 40px;
    }
    
    .hero-section {
        padding: 100px 30px 60px;
    }
    
    .hero-logo {
        width: 130px;
        height: 130px;
    }
    
    .services-section {
        padding: 60px 30px;
    }
    
    .service-card {
        width: 300px;
        height: 400px;
    }
    
    .footer-contact-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 25px;
    }
}

/* Desktop Small (992px - 1199px) */
@media (min-width: 992px) and (max-width: 1199px) {
    .container {
        max-width: 970px;
        padding: 0 30px;
    }
    
    .navbar {
        padding: 12px 30px;
        height: 80px;
    }
    
    .logo {
        width: 45px;
        height: 45px;
    }
    
    .hero-section {
        padding: 120px 40px 80px;
    }
    
    .hero-logo {
        width: 160px;
        height: 160px;
    }
    
    .services-section {
        padding: 80px 40px;
    }
    
    .footer-contact-grid {
        grid-template-columns: repeat(3, 1fr);
        gap: 30px;
    }
}

/* Desktop Large (1200px+) */
@media (min-width: 1200px) {
    .container {
        max-width: 1170px;
        padding: 0 40px;
    }
    
    .navbar {
        padding: 15px 40px;
        height: 90px;
    }
    
    .logo {
        width: 50px;
        height: 50px;
    }
    
    .hero-section {
        padding: 150px 60px 100px;
    }
    
    .hero-logo {
        width: 180px;
        height: 180px;
    }
    
    .services-section {
        padding: 100px 60px;
    }
    
    .footer-contact-grid {
        grid-template-columns: repeat(3, 1fr);
        gap: 40px;
    }
}

/* iPad Specific (768px - 1024px) */
@media (min-width: 768px) and (max-width: 1024px) {
    /* iPad Portrait */
    @media (orientation: portrait) {
        .services-grid {
            max-width: 700px;
        }
        
        .service-card {
            width: 300px;
            height: 400px;
        }
        
        .vertical-content-container {
            flex-direction: column;
            text-align: center;
        }
    }
    
    /* iPad Landscape */
    @media (orientation: landscape) {
        .services-grid {
            max-width: 1000px;
        }
        
        .vertical-content-container {
            flex-direction: row;
        }
    }
}

/* Android Specific Optimizations */
@supports (-webkit-appearance: none) {
    @media (max-width: 767px) {
        /* Android specific navbar */
        .navbar {
            backdrop-filter: blur(10px);
            -webkit-backdrop-filter: blur(10px);
        }
        
        /* Android specific slider */
        .slider {
            height: 240px;
        }
        
        .slide img {
            object-fit: cover;
            object-position: center;
        }
        
        /* Android specific vertical slider */
        .vertical-slider-section {
            padding: 30px 10px;
        }
        
        .gallery-container {
            height: 200px;
            border-radius: 12px;
        }
        
        /* Android specific animations */
        .animated-element {
            transform: translateZ(0);
            -webkit-transform: translateZ(0);
        }
    }
}

/* High DPI Displays */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
    .logo img,
    .hero-logo img,
    .footer-logo img {
        image-rendering: -webkit-optimize-contrast;
        image-rendering: crisp-edges;
    }
}

/* Landscape Orientation Adjustments */
@media (orientation: landscape) and (max-height: 500px) {
    .hero-section {
        padding: 60px 20px 40px;
        min-height: 100vh;
    }
    
    .hero-logo {
        width: 80px;
        height: 80px;
    }
    
    .slider {
        height: 200px;
        margin: 15px auto;
    }
}

/* Touch Device Optimizations */
@media (hover: none) and (pointer: coarse) {
    /* Remove hover effects on touch devices */
    .service-card:hover,
    .contact-item:hover,
    .footer-contact-item:hover {
        transform: none;
    }
    
    /* Enhance touch targets */
    .nav-links a,
    .cta-button,
    .arrow-btn,
    .dot {
        min-height: 44px;
        min-width: 44px;
        padding: 12px;
    }
    
    /* Touch feedback */
    .service-card:active,
    .cta-button:active,
    .nav-links a:active {
        transform: scale(0.95);
        opacity: 0.8;
    }
}

/* Reduced Motion Support */
@media (prefers-reduced-motion: reduce) {
    .animated-element,
    .floating-image-1,
    .floating-image-2 {
        animation: none !important;
        transition: none !important;
    }
    
    .slide img {
        transition: none !important;
    }
}
