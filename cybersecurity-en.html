<!DOCTYPE html>
<html lang="en" dir="ltr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Information Security - MCT</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="css/style.css">
    <link rel="stylesheet" href="css/virtical-slider.css">
    <link rel="stylesheet" href="css/responsive.css">
    <link rel="stylesheet" href="css/device-adaptive.css">
    <link rel="stylesheet" href="css/service-pages-navbar.css">
    <link rel="stylesheet" href="css/mobile-fixes.css">
    <style>
        /* Font Application for Cybersecurity Page */
        * {
            font-family: 'NotoNaskhArabic', 'Arial', sans-serif;
        }
        
        body {
            font-family: 'NotoNaskhArabic', 'Arial', sans-serif;
        }
        
        h1, h2, h3, h4, h5, h6 {
            font-family: 'NotoNaskhArabic', 'Arial', sans-serif;
            font-weight: 700;
        }
        
        .cta-button, button {
            font-family: 'NotoNaskhArabic', 'Arial', sans-serif;
            font-weight: 600;
        }
        
        .nav-links a {
            font-family: 'NotoNaskhArabic', 'Arial', sans-serif;
            font-weight: 500;
        }
    </style>
</head>
<body class="service-page">
    <!-- خلفية متحركة -->
    <!-- شاشة التحميل -->
    <div class="page-loader" id="pageLoader">
        <div class="loader-logo">
            <img src="image/logo-8.png" alt="MCT Logo">
            <div class="loader-text">Information Security</div>
        </div>
    </div>

    <div class="animated-bg"></div>
    <div class="floating-particles" id="particles"></div>

    <!-- Navigation -->
    <nav class="navbar">
        <div class="nav-container">
            <div class="logo-section">
                <a href="index-en.html" class="logo">
                    <img src="image/logo-11.png" alt="MCT Logo">
                </a>
                <a href="index-en.html" class="logo">
                    <img src="image/logo-10.png" alt="MCT Logo">
                </a>
            </div>
            <div class="nav-center">
                <button class="hamburger" id="hamburger-menu" aria-label="Open menu">
                    <span></span>
                    <span></span>
                    <span></span>
                </button>
                <ul class="nav-links" id="nav-links">
                    <li><a href="networking-en.html">Network Solutions</a></li>
                    <li><a href="software-en.html">Software Development</a></li>
                    <li><a href="cybersecurity-en.html" class="active">Information Security</a></li>
                    <li><a href="maintenance-en.html">Support & Maintenance</a></li>
                    <li><a href="index-en.html">Back to Home</a></li>
                    <li><button id="lang-toggle" onclick="switchLanguage('ar')">AR</button></li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <main class="main-content">
        <!-- Service Hero -->
        <section class="service-hero fade-in-element">
            <div class="service-icon"><i class="fas fa-shield-alt"></i></div>
            <h1 class="service-title fade-in-element delay-1">Information Security</h1>
            <p class="service-subtitle fade-in-element delay-2">
                We provide integrated security solutions to protect your data and systems from cyber threats. Our team of cybersecurity experts ensures comprehensive protection for your digital infrastructure.
            </p>
        </section>

        <!-- خدماتنا -->
        <section class="content-section fade-in-element delay-3">
            <h2 class="section-title fade-in-element">Our Information Security Services</h2>
            <div class="services-with-animation">
                <div class="services-text">
                    <ul class="services-list fade-in-element delay-1">
                        <li><strong>Security Risk Assessment:</strong> Comprehensive analysis of risks and identification of system vulnerabilities</li>
                        <li><strong>Infrastructure Protection:</strong> Securing networks, servers, and systems from cyber attacks</li>
                        <li><strong>Data Protection:</strong> Encryption and protection of sensitive data from unauthorized access</li>
                        <li><strong>Security Monitoring:</strong> Continuous system monitoring and early threat detection</li>
                        <li><strong>Incident Response:</strong> Rapid and effective handling of security incidents</li>
                        <li><strong>Security Training:</strong> Training programs to raise security awareness among employees</li>
                        <li><strong>Compliance Standards:</strong> Ensuring system compliance with global security standards</li>
                    </ul>
                </div>
                
                        <!-- Connection Lines -->
                        <div class="connection-lines">
                            <div class="connection-line line-1"></div>
                            <div class="connection-line line-2"></div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- المميزات -->
        <section class="content-section fade-in-element delay-4">
            <h2 class="section-title fade-in-element">Why Choose Our Services?</h2>
            <div class="features-grid">
                <div class="feature-card fade-in-element delay-1">
                    <h3>Extensive Experience</h3>
                    <p>A team of cybersecurity experts with experience in various digital security fields</p>
                </div>
                <div class="feature-card fade-in-element delay-2">
                    <h3>Integrated Solutions</h3>
                    <p>Comprehensive security solutions covering all aspects of digital infrastructure</p>
                </div>
                <div class="feature-card fade-in-element delay-3">
                    <h3>Advanced Technologies</h3>
                    <p>Use of the latest technologies and tools in cybersecurity</p>
                </div>
                <div class="feature-card fade-in-element delay-4">
                    <h3>Rapid Response</h3>
                    <p>24/7 support team available to handle security incidents</p>
                </div>
                <div class="feature-card fade-in-element delay-5">
                    <h3>Continuous Protection</h3>
                    <p>Continuous monitoring and updates to ensure maximum protection</p>
                </div>
                <div class="feature-card fade-in-element delay-6">
                    <h3>Competitive Cost</h3>
                    <p>Effective security solutions at competitive prices suitable for various budgets</p>
                </div>
            </div>
        </section>

        <!-- Call to Action -->
        <section class="contact-cta fade-in-element delay-5">
            <h2 style="color: #008B80; margin-bottom: 20px;">Do You Need Comprehensive Protection for Your Data and Systems?</h2>
            <p style="margin-bottom: 30px; color: #c0c0c0;">Contact us today for a free security consultation and comprehensive assessment of your needs</p>
            <a href="index-en.html#contact" class="cta-button">Contact Us</a>
            <a href="tel:+966123456789" class="cta-button">Call Us</a>
        </section>
    </main>

    <script>
        // إنشاء الجسيمات المتحركة
        function createParticles() {
            const particlesContainer = document.getElementById('particles');
            const particleCount = 30;

            for (let i = 0; i < particleCount; i++) {
                const particle = document.createElement('div');
                particle.className = 'particle';
                particle.style.left = Math.random() * 100 + '%';
                particle.style.top = Math.random() * 100 + '%';
                particle.style.animationDelay = Math.random() * 8 + 's';
                particle.style.animationDuration = (Math.random() * 4 + 4) + 's';
                particlesContainer.appendChild(particle);
            }
        }

        // تهيئة الصفحة
        // تأثير التبديل السلس للغة
        function switchLanguage(lang) {
            if (lang === 'ar') {
                sessionStorage.setItem('pageTransition', 'true');
                window.location.href = 'cybersecurity.html';
            }
        }

        // إدارة تحميل الصفحة
        function handlePageLoad() {
            const pageLoader = document.getElementById('pageLoader');
            const isTransition = sessionStorage.getItem('pageTransition');

            if (isTransition) {
                sessionStorage.removeItem('pageTransition');
                setTimeout(() => pageLoader.classList.add('fade-out'), 2000);
                setTimeout(() => pageLoader.style.display = 'none', 2800);
            } else {
                pageLoader.style.display = 'none';
            }
        }

        window.addEventListener('load', () => {
            handlePageLoad();
            createParticles();
        });

        // إعداد الشكل المتحرك
        document.addEventListener('DOMContentLoaded', function() {
            const dashboardContainer = document.querySelector('.dashboard-container');
            const imageOrbit = document.querySelector('.image-orbit');

            if (!dashboardContainer || !imageOrbit) return;

            let currentRotation = 0;
            let isRotating = false;
            let animationFrame;
            const rotationSpeed = 0.5;

            function animate() {
                if (isRotating) {
                    currentRotation += rotationSpeed;
                    if (currentRotation >= 360) {
                        currentRotation = currentRotation % 360;
                    }
                    imageOrbit.style.transform = `rotate(${currentRotation}deg)`;
                    animationFrame = requestAnimationFrame(animate);
                }
            }

            dashboardContainer.addEventListener('mouseenter', function() {
                if (!isRotating) {
                    isRotating = true;
                    animationFrame = requestAnimationFrame(animate);
                }
            });

            dashboardContainer.addEventListener('mouseleave', function() {
                isRotating = false;
                cancelAnimationFrame(animationFrame);
            });
        });

    </script>
    <script src="js/scroll-animations.js"></script>
    <script src="js/mobile-nav.js"></script>
    <script src="js/device-detection.js"></script>
</body>
</html>
