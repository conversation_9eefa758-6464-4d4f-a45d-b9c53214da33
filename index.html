<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=5.0, user-scalable=yes, viewport-fit=cover">
    <title>MCT</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="css/style.css">
    <link rel="stylesheet" href="css/slider.css">
    <link rel="stylesheet" href="css/virtical-slider.css">
    <link rel="stylesheet" href="css/fix-overflow.css">
    <link rel="stylesheet" href="css/responsive.css">
    <link rel="stylesheet" href="css/services-mobile.css">
    <link rel="stylesheet" href="css/contact-footer-mobile.css">
    <link rel="stylesheet" href="css/mobile-fixes.css">
    <link rel="stylesheet" href="css/device-adaptive.css">
    <link rel="stylesheet" href="css/animation-fixes.css">
    <script src="https://cdnjs.cloudflare.com/ajax/libs/gsap/3.13.0/gsap.min.js"></script>
    <script src="js/performance.js" defer></script>
    <script src="js/device-detection.js" defer></script>
    <script src="js/mobile-performance.js" defer></script>
    <script src="js/scroll-animations.js" defer></script>
    <script src="js/mobile-nav.js" defer></script>
    <script src="js/touch-slider.js" defer></script>
    <script src="js/common.js" defer></script>
</head>
<body>
    <!-- شاشة التحميل -->
    <div class="page-loader" id="pageLoader">
        <div class="loader-logo">
            <img src="image/logo-8.png" alt="MCT Logo">
            <div class="loader-text">Mojadedoon For Creative Technology</div>
        </div>
    </div>

    <!-- خلفية متحركة -->
    <div class="animated-bg"></div>
    <div class="floating-particles" id="particles"></div>
    <div class="meteor" id="meteor-1"></div>

    <!-- شريط التنقل -->
    <nav class="navbar">
        <div class="nav-container">
            <div class="logo-section">
                <div class="logo">
                    <img src="image/logo-11.png" alt="MCT Logo">
                </div>
                <div class="logo">
                    <img src="image/logo-10.png" alt="MCT Logo">
                </div>
            </div>
            <div class="nav-center">
                <button class="hamburger" id="hamburger-menu" aria-label="Open menu">
                    <span></span>
                    <span></span>
                    <span></span>
                </button>
                <ul class="nav-links" id="nav-links">
                    <li><a href="#home">الرئيسية</a></li>
                    <li><a href="#services">الخدمات</a></li>
                    <li><a href="#contact">تواصل معنا</a></li>
                    <li></li>
                    <li></li>
                    <li></li>
                    <li><button id="lang-toggle" onclick="window.switchLanguage('en')">EN</button></li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- المحتوى الرئيسي -->
    <main class="main-content content-loading" id="mainContent">
        <!-- الصفحة الرئيسية -->
        <section id="home" class="section">
            <div class="hero-section">
                <div class="hero-logo fade-in-element">
                    <img src="image/logo-8.png" alt="MCT Logo">
                </div>
                <div class="hero-content fade-in-element delay-1">
                    <p class="fade-in-element delay-2">
نحن هنا لنقدم لك حلولاً تقنية مبتكرة تلبي احتياجاتك. سواء كنت تبحث عن حلول برمجية تطوير تطبيقات ويب أو حلول أمنية، فإن فريقنا المتخصص جاهز لتقديم الدعم والمساعدة. نحن نؤمن بأن التكنولوجيا هي المفتاح لتحقيق النجاح، ونسعى جاهدين لتقديم أفضل الخدمات لعملائنا.                    </p>
                    <button class="cta-button fade-in-element delay-3" onclick="document.getElementById('services').scrollIntoView({behavior: 'smooth'})">اكتشف خدماتنا</button>
                </div>
            </div>
        </section>
        
        <!-- السلايدر الجديد -->
        <section class="slider-section">
            <div class="slider" id="slider">
                <div class="slides-wrapper" id="slidesWrapper">
                    <div class="slide active">
                        <img src="image/5.png" alt="حلول تقنية متكاملة" />
                       <!--<div class="slide-content">
                            <h2>حلول تقنية متكاملة</h2>
                            <p>نقدم أحدث الحلول التقنية المتكاملة لتنمية أعمالك ومساعدتك على النجاح في العصر الرقمي</p>
                            <a href="#services" class="cta-button">اكتشف المزيد</a>
                        </div>-->
                    </div>
                    <div class="slide">
                        <img src="image/17.jpg" alt="حلول أمنية متقدمة" />
                      <!--<div class="slide-content">
                            <h2>حلول أمنية متقدمة</h2>
                            <p>حماية شاملة لأنظمتك وبياناتك من التهديدات الإلكترونية بأحدث التقنيات</p>
                            <a href="cybersecurity.html" class="cta-button">تعرف على خدماتنا الأمنية</a>
                        </div>--> 
                    </div>
                    <div class="slide">
                        <img src="image/18.jpg" alt="تطوير برمجي مخصص" />
                       <!--<div class="slide-content">
                            <h2>تطوير برمجي مخصص</h2>
                            <p>تصميم وتطوير حلول برمجية مبتكرة تلبي احتياجات عملك بدقة عالية</p>
                            <a href="software.html" class="cta-button">ابدأ مشروعك الآن</a>
                        </div>--> 
                    </div>
                </div>
                <div class="slider-navigation">
                    <button class="arrow-btn prev-arrow" id="prevArrow">
                        <i class="fas fa-chevron-right"></i>
                    </button>
                    <div class="dots" id="dots"></div>
                    <button class="arrow-btn next-arrow" id="nextArrow">
                        <i class="fas fa-chevron-left"></i>
                    </button>
                </div>
            </div>
        </section>
        <!-- قسم الخدمات -->
        <section id="services" class="section">
            <div class="services-container">
                <div class="services-section">
                    <h2 class="section-title fade-in-element">خدماتنا</h2>
                    <div class="services-grid">
                        <a href="networking.html" class="service-card fade-in-element delay-1">
                                <div>
                                    <img src="image/1e.jpg" class="img-service" style="width: 250px; height: 290px;">
                                </div>
                            <h3>حلول الشبكات</h3>
                        </a>
                        <a href="software.html" class="service-card fade-in-element delay-1">
                                <div>
                                    <img src="image/12.jpeg" class="img-service "style="width: 250px; height: 290px;">
                                </div>
                            <h3>تطوير البرمجيات</h3>
                        </a>
                        <a href="cybersecurity.html" class="service-card fade-in-element delay-1">
                                <div>
                                    <img src="image/7e.jpg" class="img-service"style="width: 250px; height: 290px;">
                                </div>
                            <h3>أمن المعلومات</h3>
                        </a>
                     <!--   <a href="security-assessment.html" class="service-card fade-in-element delay-1">
                                <div>
                                    <img src="image/6.jpg" class="img-service">
                                </div>
                            <h3>الفحص الأمني</h3>
                        </a> -->
                        <a href="maintenance.html" class="service-card fade-in-element delay-1">
                                <div>
                                    <img src="image/14e.jpg" class="img-service" style="width: 250px; height: 290px;">
                                </div>
                            <h3>الدعم والصيانة</h3>
                        </a>
                       <!-- <a href="software.html" class="service-card fade-in-element delay-2">
                            <div class="service-icon"><i class="fas fa-code"></i></div>
                            <h3>تطوير البرمجيات</h3>
                          <p>تطوير التطبيقات والبرمجيات المخصصة باستخدام أحدث التقنيات لتلبية احتياجات العمل المختلفة.</p>
                        </a>
                        <a href="cybersecurity.html" class="service-card fade-in-element delay-3">
                            <div class="service-icon"><i class="fas fa-shield-alt"></i></div>
                            <h3>أمن المعلومات</h3>
                            <p>حماية البيانات والأنظمة من التهديدات الإلكترونية وتطبيق أفضل ممارسات الأمان الرقمي.</p>
                        </a>
                        <a href="security-assessment.html" class="service-card fade-in-element delay-4">
                            <div class="service-icon"><i class="fas fa-search"></i></div>
                            <h3>الفحص الأمني</h3>
                            <p>تقييم وفحص الأنظمة والشبكات لاكتشاف الثغرات الأمنية وتقديم التوصيات اللازمة.</p>
                        </a>
                        <a href="maintenance.html" class="service-card fade-in-element delay-5">
                            <div class="service-icon"><i class="fas fa-tools"></i></div>
                            <h3>الدعم والصيانة</h3>
                            <p>خدمات الصيانة الدورية والطارئة للأنظمة والمعدات التقنية لضمان استمرارية العمل.</p>
                        </a>-->
                    </div>
                </div>
            </div>
        </section>

        <!-- قسم السلايدر العامودي -->
        <section class="vertical-slider-section">
            <div class="decorative-image floating-image-1">
                <img src="image/programming.png" alt="C# Development">
            </div>
            <div class="decorative-image floating-image-2">
                <img src="image/networking.png" alt="Java Development">
            </div>
            <div class="vertical-content-container">
                <div class="vertical-text-content">
                    <div class="text-slide active">
                        <h3>الأمان أولاً</h3>
                        <p>في عالم اليوم المترابط، الأمان أمر بالغ الأهمية. نطبق إجراءات أمنية قوية لحماية بياناتك القيمة وضمان استمرارية عملياتك التجارية بأمان.</p>
                    </div>
                    <div class="text-slide">
                        <h3>التميز في كل مشروع</h3>
                        <p>من الفكرة إلى الإنجاز، نحافظ على أعلى معايير الجودة والاحترافية. كل مشروع نقوم به يعكس التزامنا بالتميز ورضا العملاء.</p>
                    </div>
                    <div class="text-slide">
                        <h3>دعم تقني على مدار الساعة</h3>
                        <p>فريق الدعم المخصص لدينا متاح على مدار الساعة لمعالجة احتياجاتك التقنية وضمان تشغيل أنظمتك بسلاسة في جميع الأوقات.</p>
                    </div>
                    <div class="text-slide">
                        <h3>حلول مخصصة</h3>
                        <p>نفهم أن كل عمل تجاري فريد من نوعه. لهذا السبب نخلق حلولاً مصممة خصيصاً تتوافق تماماً مع متطلباتك المحددة وأهداف عملك.</p>
                    </div>
                </div>
                <div class="vertical-slider-container">
                    <div class="gallery-container">
                        <div class="gallery-wrapper">
                            <div class="gallery-grid">
                                <div class="image-card">
                                    <img src="image/15.jpg" alt="Service Image 2">
                                </div>
                                <div class="image-card">
                                    <img src="image/10.jpg" alt="Service Image 2">
                                </div>
                                <div class="image-card">
                                    <img src="image/16.jpg" alt="Service Image 1">
                                </div>
                                <div class="image-card">
                                    <img src="image/13.jpg" alt="Service Image 2">
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>


    </main>

    <!-- تذييل الصفحة -->
    <footer class="footer" id="contact">
        <div class="footer-content">
            <a href="#home" class="footer-logo" >
             <img src="image/logo-8.png" alt="MCT Logo" >
            </a>
            <p>نقدم حلولاً تقنية مبتكرة تلبي احتياجات العصر الرقمي</p>

            <!-- معلومات التواصل -->
            <div class="footer-contact">
                <h3>تواصل معنا</h3>
                <div class="footer-contact-grid">
                    <div class="footer-contact-item">
                        <i class="fas fa-envelope"></i>
                        <div>
                            <h4>البريد الإلكتروني</h4>
                            <p><a href="mailto:<EMAIL>"><EMAIL></a></p>
                            <p><a href="mailto:<EMAIL>"><EMAIL></a></p>
                        </div>
                    </div>
                    <div class="footer-contact-item">
                        <i class="fas fa-phone"></i>
                        <div>
                            <h4>أرقام الهواتف</h4>
                            <p><a href="tel:+963951721454">+963 951 721 454</a></p>
                        </div>
                    </div>
                    <div class="footer-contact-item">
                        <i class="fas fa-map-marker-alt"></i>
                        <div>
                            <h4>العنوان</h4>
                            <p>دمشق</p>
                        </div>
                    </div>
                </div>
            </div>

          <!--<div class="footer-links">
                <a href="#home">الرئيسية</a>
                <a href="#services">الخدمات</a>
            </div>-->  
        </div>
    </footer>

    <!-- زر الانتقال للأعلى -->
    <div class="scroll-top" id="scrollTop">
        <i class="fas fa-arrow-up"></i>
    </div>

    <style>
      html, body {
        touch-action: manipulation;
        -webkit-tap-highlight-color: transparent;
        overscroll-behavior: contain;
      }
      body {
        font-size: 16px;
      }
      @media (max-width: 600px) {
        .main-content, .section, .services-container, .vertical-content-container {
          padding: 0 5px !important;
        }
        .slider {
          width: 98% !important;
          max-width: 100vw !important;
          height: 320px !important;
        }
        .slide img {
          height: 180px !important;
        }
        .slide-content h2 {
          font-size: 1.2rem !important;
        }
        .slide-content p {
          font-size: 0.9rem !important;
        }
        .navbar, .header {
          padding: 8px 2% !important;
        }
        .logo {
          width: 32px !important;
          height: 32px !important;
        }
        .cta-button {
          padding: 10px 18px !important;
          font-size: 0.95rem !important;
        }
      }
    </style>

    <script>
        document.addEventListener("DOMContentLoaded", function() {
            // إنشاء الجسيمات المتحركة
            function createParticles() {
                const particlesContainer = document.getElementById('particles');
                const particleCount = 50;

                for (let i = 0; i < particleCount; i++) {
                    const particle = document.createElement('div');
                    particle.className = 'particle';
                    particle.style.left = Math.random() * 100 + '%';
                    particle.style.top = Math.random() * 100 + '%';
                    particle.style.animationDelay = Math.random() * 5 + 's';
                    particle.style.animationDuration = (Math.random() * 3 + 3) + 's';
                    particlesContainer.appendChild(particle);
                }
            }

            // تأثير التمرير السلس
            function smoothScroll() {
                const navLinks = document.querySelectorAll('.nav-links a, .cta-button, .footer-links a');
                navLinks.forEach(link => {
                    link.addEventListener('click', (e) => {
                        if (link.getAttribute('href').startsWith('#')) {
                            e.preventDefault();
                            const targetId = link.getAttribute('href');
                            const targetSection = document.querySelector(targetId);
                            if (targetSection) {
                                targetSection.scrollIntoView({
                                    behavior: 'smooth'
                                });
                            }
                        }
                    });
                });
            }

            // تأثير شريط التنقل عند التمرير
            function navbarScrollEffect() {
                const navbar = document.querySelector('.navbar');
                window.addEventListener('scroll', () => {
                    if (window.scrollY > 100) {
                        navbar.style.background = 'rgba(15, 15, 35, 0.98)';
                        navbar.style.boxShadow = '0 10px 30px rgba(0, 0, 0, 0.3)';
                    } else {
                        navbar.style.background = 'rgba(15, 15, 35, 0.95)';
                        navbar.style.boxShadow = 'none';
                    }
                });
            }

            // تأثير الظهور التدريجي عند التمرير
            function scrollAnimations() {
                const observerOptions = {
                    threshold: 0.1,
                    rootMargin: '0px 0px -50px 0px'
                };

                const observer = new IntersectionObserver((entries) => {
                    entries.forEach(entry => {
                        if (entry.isIntersecting) {
                            entry.target.classList.add('visible');
                        }
                    });
                }, observerOptions);

                // مراقبة جميع العناصر التي تحتوي على كلاس fade-in-element
                const fadeElements = document.querySelectorAll('.fade-in-element');
                fadeElements.forEach(element => {
                    observer.observe(element);
                });
            }

            // زر الانتقال للأعلى
            function setupScrollTop() {
                const scrollTopBtn = document.getElementById('scrollTop');
                
                window.addEventListener('scroll', () => {
                    if (window.scrollY > 500) {
                        scrollTopBtn.classList.add('visible');
                    } else {
                        scrollTopBtn.classList.remove('visible');
                    }
                });
                
                scrollTopBtn.addEventListener('click', () => {
                    window.scrollTo({
                        top: 0,
                        behavior: 'smooth'
                    });
                });
            }

            // تكرار حركة الشهاب
            function randomizeMeteor() {
                const meteors = document.querySelectorAll('.meteor');
                
                meteors.forEach(meteor => {
                    meteor.style.top = Math.random() * 8 + '%';
                    meteor.style.left = Math.random() * 8 + '%';
                    meteor.style.animationDelay = Math.random() * 1 + 's';
                });
            }

            // تهيئة الموقع
            function initWebsite() {
                createParticles();
                smoothScroll();
                navbarScrollEffect();
                scrollAnimations();
                setupScrollTop();
                randomizeMeteor();
                enhanceServiceCards(); // إضافة تحسينات الخدمات
                initVerticalSlider(); // إضافة السلايدر العمودي
                setInterval(randomizeMeteor, 5000);
            }

            // سلايدر عمودي (صورة واحدة + نص واحد، تلقائي فقط)
            function initVerticalSlider() {
                const galleryWrapper = document.querySelector('.gallery-wrapper');
                const galleryGrid = document.querySelector('.gallery-grid');
                const imageCards = galleryGrid.querySelectorAll('.image-card');
                const textSlides = document.querySelectorAll('.text-slide');
                let currentIndex = 0;
                let autoScrollInterval;
                let autoScrollDelay = 4000; // 4 ثواني

                // إظهار صورة واحدة وعبارة واحدة فقط
                function showSlide(index) {
                    imageCards.forEach((card, i) => {
                        card.style.display = (i === index) ? 'block' : 'none';
                    });
                    textSlides.forEach((slide, i) => {
                        slide.classList.toggle('active', i === index);
                    });
                }

                function nextSlide() {
                    currentIndex = (currentIndex + 1) % imageCards.length;
                    showSlide(currentIndex);
                }

                function startAutoScroll() {
                    if (autoScrollInterval) clearInterval(autoScrollInterval);
                    autoScrollInterval = setInterval(nextSlide, autoScrollDelay);
                }

                // تهيئة العرض الأول
                showSlide(currentIndex);
                startAutoScroll();
            }

            // تأثير التبديل السلس للغة
            function switchLanguage(lang) {
                if (lang === 'en') {
                    // إظهار شاشة التحميل
                    const pageLoader = document.getElementById('pageLoader');
                    pageLoader.style.display = 'flex';
                    pageLoader.classList.remove('fade-out');

                    // إضافة معرف للصفحة الجديدة
                    sessionStorage.setItem('pageTransition', 'true');

                    // تأخير قصير ثم الانتقال
                    setTimeout(() => {
                        window.location.href = 'index-en.html';
                    }, 500);
                }
            }

            // إضافة الدالة للنافذة العامة
            window.switchLanguage = switchLanguage;

            // إدارة تحميل الصفحة
            function handlePageLoad() {
                const pageLoader = document.getElementById('pageLoader');
                const mainContent = document.getElementById('mainContent');
                const isTransition = sessionStorage.getItem('pageTransition');

                if (isTransition) {
                    // إذا كان انتقال من صفحة أخرى، أظهر شاشة التحميل
                    sessionStorage.removeItem('pageTransition');

                    // إخفاء شاشة التحميل بعد 2 ثانية
                    setTimeout(() => {
                        pageLoader.classList.add('fade-out');

                        // إظهار المحتوى بعد إخفاء شاشة التحميل
                        setTimeout(() => {
                            pageLoader.style.display = 'none';
                            showContentWithAnimation();
                        }, 800);
                    }, 2000);
                } else {
                    // تحميل عادي للصفحة - إخفاء شاشة التحميل فوراً
                    pageLoader.style.display = 'none';
                    // تأخير بسيط لضمان تحميل DOM
                    setTimeout(() => {
                        showContentWithAnimation();
                    }, 100);
                }
            }

            // إظهار المحتوى مع التأثيرات
            function showContentWithAnimation() {
                const mainContent = document.getElementById('mainContent');

                // إظهار المحتوى الرئيسي بتأثير fade-in
                mainContent.classList.remove('content-loading');
                mainContent.classList.add('content-loaded');

                // إضافة تأثيرات للعناصر بالتتابع
                setTimeout(() => {
                    addStaggerAnimation();
                }, 100);
            }

            // إضافة تأثير التتابع للعناصر
            function addStaggerAnimation() {
                const elements = document.querySelectorAll('.fade-in-element');
                let delay = 0;
                elements.forEach((el, idx) => {
                    // فقط العناصر الظاهرة في الشاشة
                    const rect = el.getBoundingClientRect();
                    if (rect.top < window.innerHeight && rect.bottom > 0) {
                        setTimeout(() => {
                            el.classList.add('visible');
                        }, delay);
                        delay += 60; // تسريع التتابع
                    }
                });
            }

            // إظهار شاشة الانتقال
            function showPageTransition(mainText, subText, callback) {
                // إنشاء عنصر الانتقال
                const transition = document.createElement('div');
                transition.className = 'page-transition';
                transition.innerHTML = `
                    <div class="transition-content">
                        <div class="transition-logo">
                            <img src="image/logo-8.png" alt="MCT Logo" style="width: 100%; height: 100%; object-fit: contain;">
                        </div>
                        <div class="transition-text">${mainText}</div>
                        <div class="transition-subtext">${subText}</div>
                    </div>
                `;

                document.body.appendChild(transition);

                // تفعيل الانتقال
                setTimeout(() => {
                    transition.classList.add('active');
                }, 50);

                // تنفيذ الانتقال بعد التأثير
                setTimeout(() => {
                    callback();
                }, 1200);
            }

            // تحسين تأثيرات hover للخدمات
            function enhanceServiceCards() {
                const serviceCards = document.querySelectorAll('.service-card');

                serviceCards.forEach(card => {
                    // إضافة تأثير الماوس
                    card.addEventListener('mouseenter', function() {
                        // إضافة تأثير صوتي بصري
                        this.style.transform = 'translateY(-10px) rotateX(5deg) scale(1.02)';

                        // تأثير الأيقونة
                        const icon = this.querySelector('.service-icon i');
                        if (icon) {
                            icon.style.animation = 'iconFloat 2s ease-in-out infinite';
                        }

                        // تأثير النص
                        const title = this.querySelector('h3');
                        const description = this.querySelector('p');

                        if (title) {
                            title.style.transform = 'translateX(5px)';
                            title.style.color = 'var(--accent-color)';
                        }

                        if (description) {
                            description.style.transform = 'translateX(3px)';
                            description.style.color = '#e0e0e0';
                        }
                    });

                    card.addEventListener('mouseleave', function() {
                        // إعادة تعيين التأثيرات
                        this.style.transform = 'translateY(0) rotateX(0) scale(1)';

                        const icon = this.querySelector('.service-icon i');
                        if (icon) {
                            icon.style.animation = '';
                        }

                        const title = this.querySelector('h3');
                        const description = this.querySelector('p');

                        if (title) {
                            title.style.transform = 'translateX(0)';
                            title.style.color = '';
                        }

                        if (description) {
                            description.style.transform = 'translateX(0)';
                            description.style.color = '';
                        }
                    });

                    // تأثير النقر
                    card.addEventListener('click', function(e) {
                        // تأثير الموجة عند النقر
                        const ripple = document.createElement('div');
                        ripple.style.cssText = `
                            position: absolute;
                            border-radius: 50%;
                            background: rgba(0, 139, 128, 0.6);
                            transform: scale(0);
                            animation: ripple 0.6s linear;
                            pointer-events: none;
                        `;

                        const rect = this.getBoundingClientRect();
                        const size = Math.max(rect.width, rect.height);
                        const x = e.clientX - rect.left - size / 2;
                        const y = e.clientY - rect.top - size / 2;

                        ripple.style.width = ripple.style.height = size + 'px';
                        ripple.style.left = x + 'px';
                        ripple.style.top = y + 'px';

                        this.appendChild(ripple);

                        setTimeout(() => {
                            ripple.remove();
                        }, 600);
                    });
                });
            }

            // تشغيل التهيئة عند تحميل الصفحة
            window.addEventListener('load', () => {
                handlePageLoad();
                initWebsite();
            });

            // تحقق من وجود عناصر السلايدر قبل المتابعة
            const slidesWrapper = document.getElementById('slidesWrapper');
            const slides = document.querySelectorAll('.slide');
            const dotsContainer = document.getElementById('dots');
            const prevArrow = document.getElementById('prevArrow');
            const nextArrow = document.getElementById('nextArrow');
            if (!slidesWrapper || !slides.length || !dotsContainer || !prevArrow || !nextArrow) {
                console.warn('Slider elements not found. Slider will not be initialized.');
                // إظهار رسالة للمستخدم
                const warn = document.createElement('div');
                warn.style.cssText = 'background: #ffdddd; color: #a00; padding: 20px; text-align: center; font-size: 1.2em; margin: 20px; border-radius: 10px;';
                warn.innerText = 'حدثت مشكلة في تحميل السلايدر. يرجى التأكد من تفعيل الجافاسكريبت وعدم وجود إضافات تمنع تحميل الموقع.';
                document.body.prepend(warn);
                return;
            }
            let currentIndex = 0;
            const totalSlides = slides.length;
            let animating = false;
            let autoPlayInterval;

            // إنشاء نقاط التصفح
            for(let i=0; i<totalSlides; i++) {
                const dot = document.createElement('div');
                dot.classList.add('dot');
                if(i === 0) dot.classList.add('active');
                dot.dataset.index = i;
                dotsContainer.appendChild(dot);
            }
            const dots = document.querySelectorAll('.dot');

            // تحديث النقاط
            function updateDots(index) {
                dots.forEach(dot => dot.classList.remove('active'));
                if (dots[index]) dots[index].classList.add('active');
            }

            // تحديث حالة الشريحة النشطة (تكبير وتصغير)
            function updateActiveSlide(index) {
                slides.forEach((slide, i) => {
                    slide.classList.toggle('active', i === index);
                });
            }

            // تحريك المحتوى أفقياً مع GSAP مع حركة النصوص
            function goToSlide(index) {
                if(animating || index === currentIndex) return;
                animating = true;

                const currentSlide = slides[currentIndex];
                const nextSlide = slides[index];

                const currentTexts = currentSlide.querySelectorAll('.slide-content > *');
                const nextTexts = nextSlide.querySelectorAll('.slide-content > *');

                // إخفاء نصوص الشريحة الحالية
                if (currentTexts.length) {
                  gsap.to(currentTexts, {y: 30, opacity: 0, stagger: 0.05, duration: 0.2, ease: "power2.in"});
                }

                // تحريك الشرائح أفقياً باتجاه صحيح RTL (إزاحة موجبة) - انتقال أسرع
                gsap.to(slidesWrapper, {
                    x: index * (slides[0].offsetWidth + 20) + 'px',
                    duration: 0.5,
                    ease: "power2.inOut",
                    onComplete: () => {
                        // إظهار نصوص الشريحة الجديدة
                        if (nextTexts.length) {
                          gsap.fromTo(nextTexts, 
                              {y: 30, opacity: 0}, 
                              {y: 0, opacity: 1, stagger: 0.1, duration: 0.3, ease: "power2.out"}
                          );
                        }
                        currentIndex = index;
                        updateDots(currentIndex);
                        updateActiveSlide(currentIndex);
                        animating = false;
                    }
                });
            }

            // التنقل عبر النقاط
            dots.forEach(dot => {
                dot.addEventListener('click', () => {
                    const idx = parseInt(dot.dataset.index);
                    goToSlide(idx);
                    resetAutoPlay();
                });
            });

            // التنقل عبر الأسهم
            prevArrow.addEventListener('click', () => {
                const prevIndex = currentIndex === 0 ? totalSlides - 1 : currentIndex - 1;
                goToSlide(prevIndex);
                resetAutoPlay();
            });

            nextArrow.addEventListener('click', () => {
                const nextIndex = (currentIndex + 1) % totalSlides;
                goToSlide(nextIndex);
                resetAutoPlay();
            });

            // التشغيل التلقائي
            function autoPlay() {
                autoPlayInterval = setInterval(() => {
                    let nextIndex = (currentIndex + 1) % totalSlides;
                    goToSlide(nextIndex);
                }, 4000);
            }
            function resetAutoPlay() {
                clearInterval(autoPlayInterval);
                autoPlay();
            }

            // بدء التشغيل التلقائي
            autoPlay();

            // إظهار نصوص الشريحة الأولى عند التحميل
            window.onload = () => {
                const firstTexts = slides[0].querySelectorAll('.slide-content > *');
                if (firstTexts.length) {
                  gsap.fromTo(firstTexts, 
                      {y: 30, opacity: 0}, 
                      {y: 0, opacity: 1, stagger: 0.15, duration: 0.6, ease: "power2.out"}
                  );
                }
            };

            // تحديث عرض الشرائح عند تغيير حجم النافذة (للتأكد من حساب العرض الصحيح)
            window.addEventListener('resize', () => {
                gsap.set(slidesWrapper, {x: currentIndex * (slides[0].offsetWidth + 20)});
            });

            // أضف مراقبة لتوقف التشغيل التلقائي عند إخفاء الصفحة
            document.addEventListener('visibilitychange', function() {
                if (document.visibilityState === 'hidden') {
                    clearInterval(autoPlayInterval);
                } else {
                    autoPlay();
                }
            });
        });
    </script>
    <script src="js/animation-controller.js"></script>
    <script src="js/animation-fix-final.js"></script>
</body>
</html>